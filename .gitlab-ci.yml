include:
  - project: 'globocom/gitlab-ci-files'
    ref: master
    file: '/templates/jobs/huskyci.yml'

stages:
  - prepare
  - huskyCI
  - lint
  - test
  - build
  - trigger
  - deploy
  - clearcache

cache:
  key: ${CI_COMMIT_REF_SLUG}-${CI_PROJECT_PATH}
  paths:
    - node_modules/
    - .angular/cache/
    - .cache/ms-playwright/
  policy: pull-push
  when: on_success
  untracked: false

huskyCI:
  stage: huskyCI
  extends: .huskyCI
  variables:
    HUSKYCI_IGNORE_ERRORS: 'true'
  only:
    - merge_requests
    - master

install_dependencies:
  stage: prepare
  image: node:18
  script:
    - npm config set registry
      https://artifactory.globoi.com/artifactory/api/npm/npm-repos
    - npm ci --legacy-peer-deps --no-package-lock
  cache:
    key: ${CI_COMMIT_REF_SLUG}-${CI_PROJECT_PATH}
    paths:
      - node_modules/
      - .angular/cache/
  artifacts:
    paths:
      - node_modules/
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_MERGE_REQUEST_IID'

lint:
  stage: lint
  image: node:18
  needs: [install_dependencies]
  script:
    - |
      git fetch origin $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
      FILE_TYPES="*.ts *.js *.html *.scss"
      if [ -n "$CI_MERGE_REQUEST_IID" ]; then
        FILES=$(git diff --diff-filter=d --name-only origin/${CI_MERGE_REQUEST_TARGET_BRANCH_NAME}...HEAD -- $FILE_TYPES)
      else
        FILES=$(git diff --diff-filter=d --name-only HEAD~1 -- $FILE_TYPES)
      fi

      if [ -n "$FILES" ]; then
        echo "Linting the following files:"
        echo "$FILES"
        npx eslint --quiet $FILES || exit 1
      else
        echo "No files to lint."
      fi
  rules:
    - if: '$CI_MERGE_REQUEST_IID'

prettier:
  stage: lint
  image: node:18
  needs: [install_dependencies]
  script:
    - |
      git fetch origin $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
      FILE_TYPES="*.ts *.js *.html *.scss"
      if [ -n "$CI_MERGE_REQUEST_IID" ]; then
        FILES=$(git diff --diff-filter=d --name-only origin/${CI_MERGE_REQUEST_TARGET_BRANCH_NAME}...HEAD -- $FILE_TYPES)
      else
        FILES=$(git diff --diff-filter=d --name-only HEAD~1 -- $FILE_TYPES)
      fi

      if [ -n "$FILES" ]; then
        echo "Formatting check for the following files:"
        echo "$FILES"
        npx prettier --write $FILES || exit 1
      else
        echo "No files to check formatting."
      fi
  rules:
    - if: '$CI_MERGE_REQUEST_IID'

unit_tests:
  stage: test
  image: mcr.microsoft.com/playwright:v1.43.1-jammy
  needs: [install_dependencies]
  before_script:
    - npm config set registry
      https://artifactory.globoi.com/artifactory/api/npm/npm-repos || npm config
      set registry https://registry.npmjs.org/
    - mkdir -p tmp
    - echo $CLOUDFRONT_ID_DEV > tmp/cloudfront_id
    - echo $AWS_ACCESS_KEY_ID > tmp/aws_access_key
    - echo $AWS_SECRET_ACCESS_KEY > tmp/aws_secret_key
    - echo $BUCKET_DEV > tmp/bucket
    - echo "window['WEBCHAT_KEY_ID'] = '$WEBCHAT_KEY_ID';" >> tmp/config.js
    - echo "window['WEBCHAT_KEY_SECRET'] = '$WEBCHAT_KEY_SECRET';" >>
      tmp/config.js
    - cat tmp/config.js
    - if [ ! -d "node_modules" ]; then npm i --no-package-lock
      --legacy-peer-deps; fi
    - export CHROME_BIN=$(find /ms-playwright/ -type f -name chrome | head -n 1)
    - export DISPLAY=:99
    - Xvfb :99 -screen 0 1024x768x24 > /dev/null &
  script:
    - npm run test:ci
  artifacts:
    when: always
    paths:
      - src/coverage
      - src/coverage/index.html
    reports:
      cobertura: src/coverage/index.html
  rules:
    - if: '$CI_MERGE_REQUEST_IID'

Sentry_Sourcemaps:Staging:
  stage: build
  image: node:18
  needs:
    - job: Build:Staging
      artifacts: true
    - job: Sentry_Release:Staging
      artifacts: false
  script:
    - export SENTRY_AUTH_TOKEN=$(cat tmp/sentry_auth_token)
    - export SENTRY_RELEASE=globo-ads-pme@${CI_COMMIT_TAG:-$CI_COMMIT_SHA}
    - npm run sentry:sourcemaps:stg -- --release $SENTRY_RELEASE
  dependencies:
    - Build:Staging
    - Sentry_Release:Staging
  rules:
    - if: '$CI_COMMIT_BRANCH == "staging"'

Sentry_Release:Staging:
  stage: build
  image: node:18
  needs:
    - job: Build:Staging
      artifacts: true
  script:
    - export SENTRY_AUTH_TOKEN=$(cat tmp/sentry_auth_token)
    - export SENTRY_ORG=gglobo
    - export SENTRY_PROJECT=globo-frontend-pme
    - export SENTRY_URL=https://sentry.dev.globoi.com/
    - export SENTRY_RELEASE=globo-ads-pme@${CI_COMMIT_TAG:-$CI_COMMIT_SHA}
    - npx sentry-cli --url $SENTRY_URL releases new -p $SENTRY_PROJECT
      $SENTRY_RELEASE
    - npx sentry-cli --url $SENTRY_URL releases set-commits $SENTRY_RELEASE
      --auto
    - npx sentry-cli --url $SENTRY_URL releases finalize $SENTRY_RELEASE
  rules:
    - if: '$CI_COMMIT_BRANCH == "staging"'
  dependencies:
    - Build:Staging

Sentry_Sourcemaps:Production:
  stage: build
  needs:
    - job: Build:Production
      artifacts: true
    - job: Sentry_Release:Production
      artifacts: false
  image: node:18
  script:
    - export SENTRY_AUTH_TOKEN=$(cat tmp/sentry_auth_token)
    - export SENTRY_RELEASE=globo-ads-pme@${CI_COMMIT_TAG:-$CI_COMMIT_SHA}
    - npm run sentry:sourcemaps:prod -- --release $SENTRY_RELEASE
  dependencies:
    - Build:Production
    - Sentry_Release:Production
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'

Sentry_Release:Production:
  stage: build
  image: node:18
  needs:
    - job: Build:Production
      artifacts: true
  script:
    - export SENTRY_AUTH_TOKEN=$(cat tmp/sentry_auth_token)
    - export SENTRY_ORG=gglobo
    - export SENTRY_PROJECT=globo-frontend-pme
    - export SENTRY_URL=https://sentry.globoi.com/
    - export SENTRY_RELEASE=globo-ads-pme@${CI_COMMIT_TAG:-$CI_COMMIT_SHA}
    - npx sentry-cli --url $SENTRY_URL releases new -p $SENTRY_PROJECT
      $SENTRY_RELEASE
    - npx sentry-cli --url $SENTRY_URL releases set-commits $SENTRY_RELEASE
      --auto
    - npx sentry-cli --url $SENTRY_URL releases finalize $SENTRY_RELEASE
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  dependencies:
    - Build:Production

image: docker:stable

variables:
  DOCKER_DRIVER: overlay2
  GIT_CLEAN_FLAGS: -ffdx -e node_modules/ -e .angular/
  CACHE_MAX_SIZE: '2GB'

services:
  - docker:dind

setup:Development:
  stage: prepare
  environment:
    name: development
  tags:
    - shell
  script:
    - mkdir tmp
    - echo $CLOUDFRONT_ID_DEV > tmp/cloudfront_id
    - echo $AWS_ACCESS_KEY_ID > tmp/aws_access_key
    - echo $AWS_SECRET_ACCESS_KEY > tmp/aws_secret_key
    - echo $BUCKET_DEV > tmp/bucket
    - echo "window['WEBCHAT_KEY_ID'] = '$WEBCHAT_KEY_ID';" >> tmp/config.js
    - echo "window['WEBCHAT_KEY_SECRET'] = '$WEBCHAT_KEY_SECRET';" >>
      tmp/config.js
    - cat tmp/config.js
  artifacts:
    paths:
      - tmp/
  only:
    - develop

setup:Staging:
  stage: prepare
  environment:
    name: staging
  tags:
    - shell
  script:
    - mkdir tmp
    - echo $CLOUDFRONT_ID_STG > tmp/cloudfront_id
    - echo $AWS_ACCESS_KEY_ID > tmp/aws_access_key
    - echo $SENTRY_AUTH_TOKEN_STG > tmp/sentry_auth_token
    - echo $AWS_SECRET_ACCESS_KEY > tmp/aws_secret_key
    - echo $BUCKET_STG > tmp/bucket
    - echo "window['WEBCHAT_KEY_ID'] = '$WEBCHAT_KEY_ID';" >> tmp/config.js
    - echo "window['WEBCHAT_KEY_SECRET'] = '$WEBCHAT_KEY_SECRET';" >>
      tmp/config.js
    - cat tmp/config.js
  artifacts:
    paths:
      - tmp/
  only:
    - staging

setup:Fix:
  stage: prepare
  environment:
    name: fix
  tags:
    - shell
  script:
    - mkdir tmp
    - echo $CLOUDFRONT_ID_FIX > tmp/cloudfront_id
    - echo $AWS_ACCESS_KEY_ID > tmp/aws_access_key
    - echo $AWS_SECRET_ACCESS_KEY > tmp/aws_secret_key
    - echo $BUCKET_FIX > tmp/bucket
    - echo "window['WEBCHAT_KEY_ID'] = '$WEBCHAT_KEY_ID';" >> tmp/config.js
    - echo "window['WEBCHAT_KEY_SECRET'] = '$WEBCHAT_KEY_SECRET';" >>
      tmp/config.js
    - cat tmp/config.js
  artifacts:
    paths:
      - tmp/
  only:
    - fix

setup:Production:
  stage: prepare
  environment:
    name: production
  tags:
    - shell
  script:
    - mkdir tmp
    - echo $CLOUDFRONT_ID_PRD > tmp/cloudfront_id
    - echo $AWS_ACCESS_KEY_ID_PRD > tmp/aws_access_key
    - echo $AWS_SECRET_ACCESS_KEY_PRD > tmp/aws_secret_key
    - echo $SENTRY_AUTH_TOKEN_PROD > tmp/sentry_auth_token
    - echo $BUCKET_PRD > tmp/bucket
    - echo "window['WEBCHAT_KEY_ID'] = '$WEBCHAT_KEY_ID_PRD';" >> tmp/config.js
    - echo "window['WEBCHAT_KEY_SECRET'] = '$WEBCHAT_KEY_SECRET_PRD';" >>
      tmp/config.js
    - cat tmp/config.js
  artifacts:
    paths:
      - tmp/
  only:
    - master

Build:Development:
  stage: build
  image: teracy/angular-cli
  tags:
    - gcp-runners
  environment:
    name: development
  variables:
    npm_config_legacy_peer_deps: 'true'
  before_script:
    - mkdir -p .angular/cache
    - echo "Cache contents:"
    - test -d "node_modules" && echo "✓ node_modules exists" || echo "✗ no
      node_modules"
    - test -d ".angular/cache" && echo "✓ Angular cache exists" || echo "✗ no
      Angular cache"
    - export NG_PERSISTENT_BUILD_CACHE=1
    - export NG_CLI_ANALYTICS=false
    - export NG_CACHE_FOLDER=$(pwd)/.angular/cache
    - npm config set registry
      https://artifactory.globoi.com/artifactory/api/npm/npm-repos
    - export NODE_OPTIONS=--max_old_space_size=6144
  script:
    - npm install @angular/cli@15.2.5 --save-dev --legacy-peer-deps
      --no-package-lock
    - npx ng analytics off
    - npm install --legacy-peer-deps --no-package-lock
    - npx ng build --configuration=dev --aot false --build-optimizer=false
      --index "./src/index-environments/dev/index.html" --optimization=true
      --output-hashing=all
  artifacts:
    paths:
      - dist/
      - tmp/
  only:
    - develop

Build:Staging:
  stage: build
  image: teracy/angular-cli
  tags:
    - gcp-runners
  environment:
    name: staging
  variables:
    npm_config_legacy_peer_deps: 'true'
  before_script:
    - mkdir -p .angular/cache
    - echo "Cache contents:"
    - test -d "node_modules" && echo "✓ node_modules exists" || echo "✗ no
      node_modules"
    - test -d ".angular/cache" && echo "✓ Angular cache exists" || echo "✗ no
      Angular cache"
    - export NG_PERSISTENT_BUILD_CACHE=1
    - export NG_CLI_ANALYTICS=false
    - export NG_CACHE_FOLDER=$(pwd)/.angular/cache
    - npm config set registry
      https://artifactory.globoi.com/artifactory/api/npm/npm-repos
    - export NODE_OPTIONS=--max_old_space_size=6144
  script:
    - npm install @angular/cli@15.2.5 --save-dev --legacy-peer-deps
      --no-package-lock
    - npx ng analytics off
    - npm install --legacy-peer-deps --no-package-lock
    - export RELEASE=${CI_COMMIT_TAG:-$CI_COMMIT_SHA}
    - sed -i "s/__VERSION__/${RELEASE}/g" src/environments/environment.stg.ts
    - npx ng build --configuration=stg --aot false --build-optimizer=false
      --index "./src/index-environments/stg/index.html" --optimization=true
      --output-hashing=all
  artifacts:
    paths:
      - dist/
      - tmp/
  only:
    - staging

Build:Production:
  stage: build
  image: teracy/angular-cli
  tags:
    - gcp-runners
  environment:
    name: production
  variables:
    npm_config_legacy_peer_deps: 'true'
  before_script:
    - mkdir -p .angular/cache
    - echo "Cache contents:"
    - test -d "node_modules" && echo "✓ node_modules exists" || echo "✗ no
      node_modules"
    - test -d ".angular/cache" && echo "✓ Angular cache exists" || echo "✗ no
      Angular cache"
    - export NG_PERSISTENT_BUILD_CACHE=1
    - export NG_CLI_ANALYTICS=false
    - export NG_CACHE_FOLDER=$(pwd)/.angular/cache
    - npm config set registry
      https://artifactory.globoi.com/artifactory/api/npm/npm-repos
    - export NODE_OPTIONS=--max_old_space_size=6144
  script:
    - npm install @angular/cli@15.2.5 --save-dev --legacy-peer-deps
      --no-package-lock
    - npx ng analytics off
    - npm install --legacy-peer-deps --no-package-lock
    - export RELEASE=${CI_COMMIT_TAG:-$CI_COMMIT_SHA}
    - sed -i "s/__VERSION__/${RELEASE}/g"
      src/environments/environment.production.ts
    - npx ng build --configuration=production --aot false
      --build-optimizer=false --index "./src/index-environments/prd/index.html"
      --optimization=true --output-hashing=all
  artifacts:
    paths:
      - dist/
      - tmp/
  only:
    - master

Deploy:
  stage: deploy
  tags:
    - shell
  script:
    - export AWS_ACCESS_KEY_ID=$(cat tmp/aws_access_key)
    - export AWS_SECRET_ACCESS_KEY=$(cat tmp/aws_secret_key)
    - export AWS_DEFAULT_REGION=us-east-1
    - export BUCKET=$(cat tmp/bucket)
    - echo $BUCKET
    - aws s3 rm $BUCKET --recursive
    - aws s3 sync dist/portalRelacionamento/ $BUCKET
  only:
    - master
    - staging
    - develop
    - fix

ClearCache:
  stage: clearcache
  tags:
    - shell
  script:
    - export AWS_ACCESS_KEY_ID=$(cat tmp/aws_access_key)
    - export AWS_SECRET_ACCESS_KEY=$(cat tmp/aws_secret_key)
    - export AWS_DEFAULT_REGION=us-east-1
    - export CLOUDFRONT_ID=$(cat tmp/cloudfront_id)
    - aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_ID
      --paths "/*"
  only:
    - master
    - staging
    - develop
    - fix
