/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core'
import { dataDisplayFormats } from './data/data-display-formats'
import { ActivatedRoute, Router } from '@angular/router'
import { DisplayFormats } from './models/display-formats'
import { FormatDigitalSelected } from '../dialogs/dialog-cropper-image/data/const-variables'
import { MatDialog, MatDialogConfig } from '@angular/material/dialog'
import { DialogCarouselUpload } from '../dialog-carousel-upload/dialog-carousel-upload.component'
import { DialogUploadEspecialPublicitarioComponent } from '../dialog-upload-especial-publicitario/dialog-upload-especial-publicitario.component'
import { GeradorCriativosService } from '@app/features/adstudio/services/gerador-criativos.service'
import {
  RemoteConfigs,
  RemoteConfigsService,
} from '@app/core/services/remote-configs/remote-configs.service'

@Component({
  selector: 'app-sidebar-display-format',
  templateUrl: 'sidebar-display-format.component.html',
  styleUrls: ['./sidebar-display-format.component.scss'],
})
export class SidebarDisplayFormat implements OnChanges, OnInit {
  public displayFormat: DisplayFormats | undefined
  settings: RemoteConfigs

  @Input() isOpen: boolean = false
  @Input() idCampanha: string
  @Input() isGalleryVersion: boolean = false
  @Input() maxLengthCreatives: boolean
  @Input() lengthList: number
  @Output() isOpenChange = new EventEmitter<boolean>()

  formatParam: string

  constructor(
    public route: ActivatedRoute,
    public router: Router,
    public dialog: MatDialog,
    private geradorCriativoService: GeradorCriativosService,
    private remoteConfigService: RemoteConfigsService,
  ) {}

  ngOnInit(): void {
    this.getQueryParams()
    this.getConfigRemote('globosim', 'WEB', 'GENERAL-VIEW')
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['isOpen'] && this.isOpen) {
      this.getQueryParams()
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
  }

  getQueryParams() {
    const queryParams = this.route.snapshot.queryParams
    this.formatParam = queryParams['creativeFormat']

    this.updateDisplayFormat()
  }

  updateDisplayFormat() {
    this.displayFormat = dataDisplayFormats.find(
      format => format?.tag === this.formatParam,
    )
  }

  get formatDigitalSelected() {
    return FormatDigitalSelected
  }

  closeSidebar() {
    this.isOpen = false
    this.isOpenChange.emit(this.isOpen)
  }

  selectedFormatModalUpload() {
    const dialogConfig = new MatDialogConfig()
    dialogConfig.disableClose = true
    dialogConfig.autoFocus = false
    dialogConfig.panelClass = 'custom-crop-dialog'
    dialogConfig.width = '820px'
    dialogConfig.minHeight = '430px'

    if (this.formatParam === FormatDigitalSelected.CAROUSEL) {
      this.dialog.open(DialogCarouselUpload, dialogConfig)
    } else {
      this.dialog.open(DialogUploadEspecialPublicitarioComponent, dialogConfig)
    }
  }

  viewPreview(param: string) {
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['digital/criativo/pre-visualizacao'], {
        queryParams: {
          creativeFormat: param,
          idCampanha: this.idCampanha,
        },
      }),
    )

    window.open(url, '_blank')
  }

  openSelectedFormatModal() {
    this.selectedFormatModalUpload()
    this.closeSidebar()
  }

  openModalGenerateCreative() {
    this.geradorCriativoService.handlePanelState(true)
    this.closeSidebar()
  }
  checkDisabledIa() {
    return this.maxLengthCreatives ? true : false
  }
  // Obter as configurações remotas do BACKSTAGE
  getConfigRemote(tenant, group, scope) {
    this.remoteConfigService
      .getConfigsRemote(tenant, group, scope)
      .subscribe(data => {
        this.settings = data
      })
  }
  isNotSpecialFormat(): boolean {
    return (
      this.formatParam !== FormatDigitalSelected.CAROUSEL &&
      this.formatParam !== FormatDigitalSelected.ESPECIAL_PUBLICITARIO &&
      this.formatParam !== FormatDigitalSelected.IN_STREAM_VIDEO &&
      this.formatParam !== FormatDigitalSelected.RETANG &&
      this.formatParam !== FormatDigitalSelected.MEGABANNER &&
      this.formatParam !== FormatDigitalSelected.HALFPAGE
    )
  }
}
