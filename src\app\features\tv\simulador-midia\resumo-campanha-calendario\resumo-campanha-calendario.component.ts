/* eslint-disable no-self-assign */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Location } from '@angular/common'
import {
  Component,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core'
import {
  MatLegacyDialog as MatDialog,
  MatLegacyDialogConfig as MatDialogConfig,
} from '@angular/material/legacy-dialog'
import { MatSort } from '@angular/material/sort'
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table'
import { ActivatedRoute, Router } from '@angular/router'
import { SimulacaoMultiexibidoraService } from '@app/core/services/no-auth/simulacao-multiexibidora/simulacao-multiexibidora.service'
import { SimulacaoV2Service } from '@app/core/services/no-auth/simulacao-v2/simulacao-v2.service'
import { SimulacaoService } from '@app/core/services/no-auth/simulacao/simulacao.service'
import { NotificationService } from '@app/core/services/notification/notification.service'
import { RepositorioImagemService } from '@app/core/services/repositorio-imagem/repositorio-imagem.service'
import { DialogCalendarioComponent } from '@app/shared/components/dialogs/dialog-calendario/dialog-calendario.component'
import { DialogConfirmarDatasAnuncioComponent } from '@app/shared/components/dialogs/dialog-confirmar-datas-anuncio/dialog-confirmar-datas-anuncio.component'
import { SimulatorRoutes } from '@app/shared/constants/simulator-routes'
import { DateRangePickerEvent } from '@app/shared/models/date-range-picker-event'
import { DefaultDialogGenericaButton } from '@app/shared/models/default-dialog-generica-button'
import { Dia } from '@app/shared/models/dia'
import { DialogAvisoData } from '@app/shared/models/dialog-aviso-data'
import { EstadoCampanhaEnum } from '@app/shared/models/enums/estado-campanha'
import { FluxoCompraEnum } from '@app/shared/models/enums/fluxo-compra'
import { TipoAviso } from '@app/shared/models/enums/tipo-aviso'
import { TipoIcone } from '@app/shared/models/enums/tipo-icone'
import { FiltroRecomendacao } from '@app/shared/models/filtro-recomendacao'
import { LabelValue } from '@app/shared/models/label-value'
import { LinhaCalendario } from '@app/shared/models/linhas-calendario'
import { ParametroSLASimulacao } from '@app/shared/models/ParametroSLASimulacao'
import { ProgramaCalendario } from '@app/shared/models/programa-calendario'
import { ProgramaSimulacao } from '@app/shared/models/programa-simulacao'
import { ResultadoSimulacao } from '@app/shared/models/resultado-simulacao'
import { ServiceLoading } from '@app/shared/models/service-loading'
import { TipoExibicaoCampanha } from '@app/shared/models/tipo-exibicao-campanha'
import { Calendario } from '@app/shared/utils/calendario'
import { DefaultDialogGenerica } from '@app/shared/utils/default-dialog-generica'
import {
  BtnDialogRevisarPlanoValueActions,
  DialogHelper,
} from '@app/shared/utils/dialog-helper'
import { FooterActions } from '@app/shared/utils/resumo-campanha-footer'
import { ServicesLoading } from '@app/shared/utils/services-loading'
import { SimuladorHelper } from '@app/shared/utils/simulador-helper'
import { SimuladorReduxService } from '@app/shared/utils/simulador-redux-service'
import { SimulatorFlow } from '@app/shared/utils/simulator-flow'
import {
  exclamacaoPurple,
  globoFridayCoupomText,
} from '@app/shared/utils/swal-templates'
import { ServiceName } from '@app/features/tv/simulador-midia-v2/resumo-campanha-multiexibidora/shared/serviceName.enum'
import { AppState } from '@app/store/app.model'
import { INITIAL_STATE } from '@app/store/initial-state'
import { UnavailableProgramnsAction } from '@app/store/programas-indisponiveis/programas-indisponiveis-data.actions'
import {
  CupomUtilizado,
  ExhibitorData,
  ProgramsData,
} from '@app/store/simulator/simulator-data.model'
import { SimulatorActions } from '@app/store/simulator/simulator.actions'
import { StepV2 } from '@app/store/simulator/simulator.model'
import { tipoExperienciaValue } from '@app/store/tipo-experiencia/tipo-experiencia.model'
import { State, Store } from '@ngrx/store'
import * as moment from 'moment'
import { Observable, Subject, Subscription } from 'rxjs'
import { first, takeUntil } from 'rxjs/operators'
import { DataToDialogs } from './utils/data-to-dialogs'
import { AdvertisementStorageService } from '@app/core/services/advertisement-service/advertisement-storage.service'
import { BiometryStatus } from '@app/shared/models/enums/biometry-status'
import { InvitationStatus, UserInfoResponse } from '@app/shared/models/invites'
import { JwtHelper } from '@app/shared/utils/jwt-helper'
import { UserType } from '@app/shared/models/enums/user-type'
import { BiometryHelper } from '@app/shared/utils/biometry-helper'
import { UserControlService } from '@app/core/services/user-control/user-control.service'
import { GtmHandlerService } from '@app/core/services/gtm/gtm-handler.service'
import { BeginCheckout } from '@app/core/services/gtm/model/gtm-data'
import { snakeCase } from 'lodash'
import { tipoExperienciaGTMValue } from '@app/shared/utils/gtm-values-util'
import { IncentivizedDiscountService } from '@app/core/services/incentivized-dicount/incentivized-discount.service'
import { isMobile } from '@app/shared/utils/mobile.helper'
import { getMediaPlanSteps } from '@app/shared/utils/media-plan'
import { TimeLineProps } from '@app/shared/models/timeline'
import { InvitationService } from '@app/core/services/invitation-service/invitation.service'

const SERVICE_NAMES = ServiceName

@Component({
  selector: 'simulador-midia-resumo-campanha-calendario',
  templateUrl: './resumo-campanha-calendario.component.html',
  styleUrls: ['./resumo-campanha-calendario.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ResumoCampanhaCalendarioComponent implements OnInit, OnDestroy {
  userType: string
  biometry_status: BiometryStatus
  inviteStatus: InvitationStatus = InvitationStatus.EM_ESPERA
  inviteStatus$: Observable<InvitationStatus>

  diaTESTE = {
    'Thu Aug 12 2021 00:00:00 GMT-0300 (Horário Padrão de Brasília)': 1,
  }
  diaT = {
    id: 'Thu Aug 12 2021 00:00:00 GMT-0300 (Horário Padrão de Brasília)',
  }

  @Input('totalPrice') totalPrice: number
  @Input() posVendaMode: boolean = false

  headerConfig = {
    headerTitle: 'em exibição na Globo',
    handlePrevious: () => this._location.back(),
    pageTitle: 'Acompanhe as datas do seu anúncio em exibição',
  }

  totalGeral: number = 0

  minDate: Date = new Date()
  minDateMoment = moment()
  maxDateMoment = moment(this.minDate).add(3, 'month')

  columnsToDisplay: string[] = []
  diasSemana = ['D', 'S', 'T', 'Q', 'Q', 'S', 'S']
  diasDoMes: Dia[] = []
  data: Date = new Date()
  primeiraVeiculacao: Date
  calendarioDesabilitado: boolean = false

  mostrarAvancar: boolean = true
  mostrarRecuar: boolean = true
  @Input() id: number

  parametrosSLA: ParametroSLASimulacao = null

  simulacao: any = {}

  showDisclaimerPriceChange = false

  advertisements: number
  advertisementDescription: string
  breakAdvertisementDescription: string[] = []
  programs: ProgramaCalendario[] = []
  exibidoras: any[]

  previousTotalValue = 0

  @ViewChild(MatSort) sort: MatSort
  dataSource: MatTableDataSource<LinhaCalendario>

  diasSemanaProgramas = []

  fecharMensagemSubscription: Subscription
  gerarVeiculacoesSubscription: Subscription
  slasSubscription: Subscription

  botaoComprarEnabled: boolean = false
  invalidPeriod: boolean = false

  salvarRascunhoSubscription: Subscription

  editouVeiculacoes: boolean = false
  edicao: boolean
  voltarVisible: boolean = false

  temInsercaoCompensada: boolean = false
  temInsercaoFalhada: boolean = false
  temInsercaoExibida: boolean = false
  temInsercaoProgramada: boolean = false
  temInsercaoAbatida: boolean = false

  campaignName$: Observable<string>
  initialDate$: Observable<string>
  finalDate$: Observable<string>
  videoDuration$: Observable<number>
  estimatedAudience$: Observable<number>
  programs$: Observable<ProgramsData[]>
  exibithors$: Observable<ExhibitorData[]>

  programsSubscription: Subscription

  diasDoMesCalendario
  bloquearRecuarMes: boolean = false

  fluxoCompra: number
  EstadoCampanhaEnum = EstadoCampanhaEnum
  destroy$ = new Subject()
  insercoesTotais = 0
  messgeInfoPriceChange =
    'Os preços exibidos podem variar de acordo com os dias de exibição do seu anúncio'
  services: ServiceLoading[] = [
    { name: SERVICE_NAMES.GET_SIMULACAO, loading: true },
    { name: SERVICE_NAMES.GET_SLAS, loading: true },
    { name: SERVICE_NAMES.GET_DIAS_SEMANA_PROGRAMAS, loading: true },
    { name: SERVICE_NAMES.ASSOCIAR_SIMULACAO, loading: false },
    { name: SERVICE_NAMES.GERAR_VEICULACOES, loading: false },
    { name: SERVICE_NAMES.ALTERAR_PROGRAMAS, loading: false },
    { name: SERVICE_NAMES.FILTRAR_RECOMENDACAO, loading: false },
    { name: SERVICE_NAMES.CONFIRMAR_RESERVA, loading: false },
    { name: SERVICE_NAMES.REGERAR_PROGRAMAS, loading: false },
    { name: SERVICE_NAMES.ALTERAR_DATA, loading: false },
  ]
  servicesLoading: ServicesLoading

  showDateRangePicker: boolean = false
  dateRangePickerVisible: boolean = false

  tiposExibicao: LabelValue[] = [
    {
      label: 'Exibir o quanto antes',
      value: TipoExibicaoCampanha.EXIBIR_QUANTO_ANTES,
    },
    {
      label: 'Exibir ao longo do período',
      value: TipoExibicaoCampanha.EXIBIR_LINEAR,
    },
  ]

  tipoExibicaoSelecionada: TipoExibicaoCampanha = this.tiposExibicao[0].value

  exibicoesMaximas: LabelValue[] = [
    { label: '1', value: 1 },
    { label: '2', value: 2 },
    { label: '3', value: 3 },
  ]

  exibicoesMaximasSelecionada: number = this.exibicoesMaximas[2].value

  filtrarRecomendacaoSubscription: Subscription
  confirmarReservaSuscription: Subscription

  loading$: Observable<boolean>

  currentStepV2: number
  stepsV2: StepV2[]
  stepSubscription: Subscription

  dataInicio: Date
  dataFim: Date
  exibirFiltrosRecomendacao: boolean = true
  user: UserInfoResponse

  textoSemFiltro =
    'Analisamos os dados preenchidos e geramos a sugestão abaixo para a exibição do seu anúncio. Você pode alterá-la ou clicar em Solicitar Anúncio para finalizar.'

  textoComFiltro =
    'Utilize os filtros abaixo para gerar uma nova sugestão de exibição ou clique em Alterar Datas para fazer a alteração por programa.'

  beginCheckoutGTMData: BeginCheckout = {
    coupon: '',
    items: [],
  }

  @Input() messageInfo = 'Acompanhe as datas do seu anúncio em exibição.'

  public hideChangeDate: boolean = false
  hasIncentivizedDiscount: boolean = false
  isMobile: boolean = false
  mediaPlan: TimeLineProps[] = []

  constructor(
    private dialog: MatDialog,
    private _location: Location,
    private activeRouter: ActivatedRoute,
    private simulacaoService: SimulacaoService,
    private repositorioImagemService: RepositorioImagemService,
    private router: Router,
    private store: Store,
    protected state: State<AppState>,
    private calendario: Calendario,
    private simuladorReduxService: SimuladorReduxService,
    private simulatorRoutes: SimulatorRoutes,
    private simulatorFlow: SimulatorFlow,
    private defaultDialog: DefaultDialogGenerica,
    private simuladorHelper: SimuladorHelper,
    private simulacaoV2Service: SimulacaoV2Service,
    private dialogHelper: DialogHelper,
    private dataToDialog: DataToDialogs,
    private simulacaoMultiexibidoraService: SimulacaoMultiexibidoraService,
    private notificationService: NotificationService,
    private jwtHelper: JwtHelper,
    private biometryHelper: BiometryHelper,
    private advertisementStorageService: AdvertisementStorageService,
    private userControlService: UserControlService,
    private gtmHandlerService: GtmHandlerService,
    private dicountIncentivizedService: IncentivizedDiscountService,
    private invitationService: InvitationService,
  ) {
    this.biometryHelper.setApprovedCallback(this.handleApproved)

    this.initializeObservableValues()

    this.simulatorFlow.init(this.simulatorRoutes.resumoCampanha)

    this.servicesLoading = new ServicesLoading(this.services)

    this.loading$ = this.servicesLoading.anyServiceLoading$()

    this.salvarRascunhoSubscription =
      this.simulacaoService.alterarProgramasData$
        .pipe(takeUntil(this.destroy$))
        .subscribe(result => {
          this.getSimulacao(true)
          this.getDiasDoMes(this.data)
          this.servicesLoading.setService({
            name: SERVICE_NAMES.ALTERAR_PROGRAMAS,
            loading: false,
          })
        })

    this.gerarVeiculacoesSubscription =
      this.simulacaoService.gerarVeiculacoesData$
        .pipe(takeUntil(this.destroy$))
        .subscribe(resultGerarVeiculacao => {
          this.getSimulacao()
          this.getDiasDoMes(this.data)
          this.servicesLoading.setService({
            name: SERVICE_NAMES.GERAR_VEICULACOES,
            loading: false,
          })
        })

    this.slasSubscription = this.simulacaoService.getSLASimulacaoData$
      .pipe(takeUntil(this.destroy$))
      .subscribe(slas => {
        this.servicesLoading.setService({
          name: SERVICE_NAMES.GET_SLAS,
          loading: false,
        })
        if (slas) {
          this.parametrosSLA = slas
          this.ajustaDataMinimaParametro()
        }
      })

    this.filtrarRecomendacaoSubscription =
      this.simulacaoService.filtrarRecomendacaoData$.subscribe(response => {
        this.servicesLoading.setService({
          name: SERVICE_NAMES.FILTRAR_RECOMENDACAO,
          loading: false,
        })
        this.getSimulacao(true)
      })

    /* BreadCrumb */
    this.stepSubscription = this.store
      .select((state: AppState) => state.simulator.currentStepV2)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        const simulator = this.state.getValue().simulator
        this.currentStepV2 = simulator.currentStepV2
        this.stepsV2 = simulator.stepsV2.filter(
          simulatorStep => simulatorStep.step >= 3,
        )
      })
    /**/

    this.confirmarReservaSuscription =
      this.simulacaoService.confirmarReservaData$
        .pipe(takeUntil(this.destroy$))
        .subscribe(response => {
          this.servicesLoading.setService({
            name: SERVICE_NAMES.CONFIRMAR_RESERVA,
            loading: false,
          })

          if (response.error) {
            return this.notificationService.globoSimCommon(
              'Ops, parece que tivemos um imprevisto.',
              response.error.message,
              'ENTENDI',
            )
          }

          if (!response.comprado) {
            this.store.dispatch(SimulatorActions.setIsPaymentEnabled(true))
            this.simulatorFlow.nextScreen(this.simulatorRoutes.resumoCampanha)
          } else {
            this.router.navigate(['/resposta-confirmacao/exibicao'])
          }
        })

    this.getDiasDoMes(this.data)
    this.verificarBloquearRecuarMes()
  }

  choosedDate(event: DateRangePickerEvent) {
    this.dateRangePickerVisible = false
    const initialDate = event.startDate.format('yyyy-MM-DD')
    const finalDate = event.endDate.format('yyyy-MM-DD')

    this.validateCoupomDatelimit(initialDate, finalDate)

    if (!this.invalidPeriod) {
      this.store.dispatch(
        SimulatorActions.changeValue('initialDate', initialDate),
      )

      this.store.dispatch(SimulatorActions.changeValue('finalDate', finalDate))
    }
  }

  private validateCoupomDatelimit(startDate: string, finalDate: string) {
    const simulator = this.state.getValue().simulator.data

    if (
      simulator.pagamentoSimulacaoControle &&
      simulator.pagamentoSimulacaoControle.status === 1 &&
      simulator.cuponsUtilizados.length > 0
    ) {
      const dateObjectStart = new Date(startDate).getTime()
      const dateObjectEnd = new Date(finalDate).getTime()

      let lastInvalidVoucher: CupomUtilizado

      this.invalidPeriod =
        simulator.cuponsUtilizados.filter((response: CupomUtilizado) => {
          lastInvalidVoucher = response
          if (response.dataLimiteExibicao) {
            const formatedDate = new Date(response.dataLimiteExibicao).getTime()
            return (
              formatedDate < dateObjectStart || formatedDate < dateObjectEnd
            )
          } else {
            return false
          }
        }).length > 0

      if (this.invalidPeriod) {
        const formatedLimitDate = new Date(
          lastInvalidVoucher.dataLimiteExibicao + 'T00:00:00-0300',
        ).toLocaleDateString()

        const vouncherMessage = globoFridayCoupomText
          .replace('CODIGO_CUPOM', lastInvalidVoucher.codigoCupom)
          .replace('LIMIT_DATE', formatedLimitDate)

        this.notificationService.globoSimCommon(
          `Selecionar datas de anúncio até  ${formatedLimitDate}`,
          vouncherMessage,
          'ENTENDI',
          {
            size: 470,
            additionalClasses: {
              confirmButton: 'swal-lone-button-default--small',
            },
            customIcon: exclamacaoPurple,
          },
        )
      }
    }
  }

  openDateRangePicker() {
    this.showDateRangePicker = true
    this.dateRangePickerVisible = true
    // this.hasSelectedDate = false;
  }

  transformDate(event) {
    return moment(event).format('yyyy-MM-DD')
  }

  ngOnInit() {
    this.id = Number(this.activeRouter.snapshot.queryParamMap.get('idCampanha'))
    this.getUserInfo()
    this.store.dispatch(SimulatorActions.setStepV2(6))

    const idCampanha =
      this.activeRouter.snapshot.queryParamMap.get('idCampanha')

    console.log(idCampanha)

    this.id = parseInt(idCampanha)
    this.fluxoCompra = this.state.getValue().simulator.data.fluxoCompra

    this.simuladorHelper.validarStatusAlteracao(this.id, retorno => {
      this.edicao = retorno
      this.getTotalGeral()
      this.simulacaoService.gerarVeiculacoes(this.id)

      if (
        this.state.getValue().simulator.data.campaignStatus.toUpperCase() ===
          EstadoCampanhaEnum.PLANO_MIDIA_PENDENTE &&
        this.fluxoCompra === FluxoCompraEnum.ANTECIPADO
      ) {
        this.voltarVisible = true
      }

      this.getSLAs()
    })

    this.getDataUser()
    this.getDiscount()

    this.isMobile = isMobile()
  }

  initializeObservableValues() {
    this.campaignName$ = this.store.select(
      (state: AppState) => state.simulator.data.campaignName,
    )

    this.initialDate$ = this.store.select(
      (state: AppState) => state.simulator.data.initialDate,
    )

    this.finalDate$ = this.store.select(
      (state: AppState) => state.simulator.data.finalDate,
    )

    this.videoDuration$ = this.store.select(
      (state: AppState) => state.simulator.data.videoDuration,
    )

    this.estimatedAudience$ = this.store.select(
      (state: AppState) => state.simulator.data.estimatedAudience,
    )

    this.programs$ = this.store.select(
      (state: AppState) => state.simulator.data.programs,
    )

    this.exibithors$ = this.store.select(
      (state: AppState) => state.simulator.data.exibithors,
    )
  }

  getTotalGeral() {
    const dadosSimulacao = this.state.getValue().simulator.data

    if (
      !!dadosSimulacao &&
      !!dadosSimulacao.pagamentoSimulacaoControle &&
      dadosSimulacao.listAbrangencia.length > 1
    ) {
      this.totalGeral =
        dadosSimulacao.pagamentoSimulacaoControle.valorLiquidoMidia
    } else {
      this.totalGeral = dadosSimulacao.programs.reduce((acc, programa) => {
        return acc + Number(programa.price * programa.advertisements)
      }, 0)
    }
  }

  setExibithors() {
    const newExibidoras = []

    this.exibidoras.forEach(async (exibidora: any) => {
      const exibidorainfo = this.simulacaoV2Service
        .getInfoExibidoraV2(exibidora.cdMunicipio)
        .toPromise()

      newExibidoras.push({
        ...exibidora.municipio,
        municipiosLength: (await exibidorainfo).municipiosCobertura?.length,
      })
    })

    this.exibidoras = newExibidoras
  }

  async visualizarMunicipiosCobertos(endereco) {
    const newEndereco = {
      ...endereco,
      address: {
        city: endereco.descricao,
        uf: endereco.cdUf,
        nomeExibidora: endereco.nomeExibidora,
      },
    }

    const exibidoraInfo = await this.simulacaoV2Service
      .getInfoExibidoraV2(newEndereco.cdMunicipio)
      .toPromise()

    this.dialogHelper.openDialogMunicipiosCobertos(newEndereco, exibidoraInfo)
  }

  formatarSimulacaoParaRedux(simulacao: any, setInitial: boolean) {
    const paymentType =
      this.state.getValue().simulator.data.paymentInversionVideoType

    this.simuladorReduxService.formatarSimulacaoParaRedux(simulacao)

    if (setInitial) {
      this.store.dispatch(
        SimulatorActions.setStep(INITIAL_STATE.simulator.step),
      )
    }

    this.loadPaymentTypeAfterReset(paymentType)
  }

  loadPaymentTypeAfterReset(paymentType: string) {
    this.store.dispatch(
      SimulatorActions.changeValue('paymentInversionVideoType', paymentType),
    )
  }

  getProgramasRedux(programas: any): ProgramsData[] {
    return programas.map(programa => {
      const program: ProgramsData = {
        id: undefined,
        cdProgram: null,
        name: programa.nomePrograma,
        programME: programa.mePrograma,
        picture: this.buscarImagemPrograma(programa.mePrograma),
        advertisements: programa.quantidade ? programa.quantidade : 0,
        weekDays: programa.diasSemana,
        estimatedAudience: programa.impacto,
        price: programa.valorCusto,
      }

      return program
    })
  }

  ngOnDestroy() {
    this.destroy$.next()
    this.destroy$.complete()
    this.previousTotalValue = 0
  }

  private async getSimulacao(
    formataRedux: boolean = false,
    setInitialRedux: boolean = false,
  ) {
    this.servicesLoading.setService({
      name: SERVICE_NAMES.GET_SIMULACAO,
      loading: true,
    })

    const simulacao = await this.simulacaoMultiexibidoraService
      .getSimulacaoMultiExibidora(this.id)
      .toPromise()

    this.servicesLoading.setService({
      name: SERVICE_NAMES.GET_SIMULACAO,
      loading: false,
    })

    this.simulacao = simulacao
    this.setDataGTM(this.simulacao)

    if (simulacao.listAbrangencia.length > 0) {
      this.exibidoras = simulacao.listAbrangencia
      this.setExibithors()
    } else {
      const exibidorainfo = await this.simulacaoV2Service
        .getInfoExibidoraV2(simulacao.cdMunicipio)
        .toPromise()

      const descricao = exibidorainfo.municipiosCobertura.find(
        municipio => municipio.cdMunicipio === simulacao.cdMunicipio,
      ).descricao

      const cdUf = exibidorainfo.municipiosCobertura.find(
        municipio => municipio.cdMunicipio === simulacao.cdMunicipio,
      ).cdUf

      this.exibidoras = [
        {
          cdMunicipio: simulacao.cdMunicipio,
          municipio: {
            ...exibidorainfo,
            descricao: descricao,
            cdUf: cdUf,
            cdMunicipio: simulacao.cdMunicipio,
          },
          cdUf: cdUf,
        },
      ]

      this.setExibithors()
    }

    if (formataRedux) {
      this.formatarSimulacaoParaRedux(simulacao, setInitialRedux)
    }

    const tipoExperiencia = this.state.getValue().simulator.data.tipoExperiencia
    const codeExhibitor = this.state.getValue().simulator.data.exhibitorCode
    this.mediaPlan = getMediaPlanSteps(tipoExperiencia, this.id, codeExhibitor)
    if (
      simulacao.listAbrangencia.length > 1 ||
      tipoExperiencia === tipoExperienciaValue.PACOTE
    ) {
      if (simulacao.pagamentoSimulacaoControle) {
        this.totalGeral = simulacao.pagamentoSimulacaoControle.valorLiquidoMidia
      }
    } else {
      if (!this.totalGeral || this.totalGeral === 0) {
        this.totalGeral = 0

        this.simulacao.programas.forEach(programa => {
          this.totalGeral += programa.valorTotal
        })
      }
    }

    this.showDisclaimerPriceChange =
      this.previousTotalValue > 0 && this.previousTotalValue !== this.totalGeral

    this.previousTotalValue = this.totalGeral

    this.data = new Date(simulacao.dataInicio + 'T12:00')
    this.getDiasDoMes(this.data)

    this.getDiasSemanaProgramas()

    this.hideChangeDateBtns()
  }

  getSLAs() {
    this.servicesLoading.setService({
      name: SERVICE_NAMES.GET_SLAS,
      loading: true,
    })

    this.simulacaoService.getSLASimulacao(this.id)
  }

  ajustaDataMinimaParametro() {
    if (this.parametrosSLA && this.parametrosSLA.totalDias) {
      this.minDate.setDate(
        this.minDate.getDate() + this.parametrosSLA.totalDias,
      )

      this.minDateMoment.add('days', this.parametrosSLA.totalDias)
    }
  }

  diffDataComHoje(data: Date): number {
    const hoje: Date = new Date()

    const diffAno = data.getUTCFullYear() - hoje.getUTCFullYear()

    if (diffAno !== 0) {
      return diffAno > 0 ? 1 : -1
    } else {
      const diffMes = data.getUTCMonth() - hoje.getUTCMonth()

      if (diffMes !== 0) {
        return diffMes > 0 ? 1 : -1
      } else {
        const diffDia = data.getUTCDate() - hoje.getUTCDate()

        if (diffDia === 0) {
          return 0
        } else {
          return diffDia > 0 ? 1 : -1
        }
      }
    }
  }

  getDiasSemanaProgramas() {
    this.servicesLoading.setService({
      name: SERVICE_NAMES.GET_DIAS_SEMANA_PROGRAMAS,
      loading: true,
    })

    this.simulacaoService.getDiasSemanaProgramas().subscribe(diasProgramas => {
      this.servicesLoading.setService({
        name: SERVICE_NAMES.GET_DIAS_SEMANA_PROGRAMAS,
        loading: false,
      })

      this.diasSemanaProgramas = diasProgramas

      this.formatarProgramas()

      this.formatarDiasDoMes()
    })
  }

  formatarProgramas() {
    let primeiraVeiculacao

    const programs: ProgramaCalendario[] = this.simulacao.programas.map(
      programa => new ProgramaCalendario(programa),
    )

    this.programs = programs.map(program => {
      // encontra o programa respectivo na lista de dias da semana
      const programFound = this.diasSemanaProgramas.find(
        x => x.sigla_programa === program.mePrograma,
      )

      // verificar se o objeto diasSemana.dias_semana possui todos os dias como false
      const allWeekDaysFalse = Object.values(programFound.dias_semana).every(
        day => day === false,
      )

      if (programFound && allWeekDaysFalse) {
        console.warn(
          `PROGRAMA: ${program.mePrograma} pode estar com erro no cadastro de dias da semana, todos os dias estão como "false"`,
        )
      }

      const allDaysUnavailable = {
        dom: false,
        seg: false,
        ter: false,
        qua: false,
        qui: false,
        sex: false,
        sab: false,
      }

      // Adiciona os dias da semana no programa sendo mapeado.
      // Se não encontrar o programa, usa um objeto com todos os dias false
      program.diasSemana = programFound
        ? programFound.dias_semana
        : allDaysUnavailable

      program.diaDoMesInsercao = {}
      program.diaDoMesCompensacao = {}
      program.diaDoMesFalha = {}
      program.diaDoMesAbatida = {}

      program.veiculacoes.forEach(veiculacao => {
        const date = new Date(veiculacao.dia)
        // Data vem um dia anterior as 21h. Criando nova data com um dia a mais para retornar a data correta.

        const dataCorreta = new Date(
          date.getFullYear(),
          date.getMonth(),
          date.getDate() + 1,
        )

        if (
          !primeiraVeiculacao ||
          dataCorreta.getTime() < primeiraVeiculacao.getTime()
        ) {
          primeiraVeiculacao = dataCorreta
        }

        if (!this.isTelaAcompanhamento) {
          program.diaDoMesInsercao[dataCorreta.toString()] =
            veiculacao.quantidadeInsercoes
        } else {
          const diffData = this.diffDataComHoje(dataCorreta)

          program.diaDoMesInsercao[dataCorreta.toString()] =
            veiculacao.quantidadeConfirmadas

          // Se já passou a data, uma inserção COMPENSADA vira EXIBIDA
          if (diffData >= 0) {
            program.diaDoMesCompensacao[dataCorreta.toString()] =
              veiculacao.quantidadeCompensadas
          } else {
            program.diaDoMesInsercao[dataCorreta.toString()] +=
              veiculacao.quantidadeCompensadas
          }

          program.diaDoMesFalha[dataCorreta.toString()] =
            veiculacao.quantidadePendentes + veiculacao.quantidadeFalhas

          program.diaDoMesAbatida[dataCorreta.toString()] =
            veiculacao.quantidadeAbatidas

          if (
            veiculacao.quantidadeConfirmadas &&
            veiculacao.quantidadeConfirmadas > 0
          ) {
            if (diffData >= 0) {
              this.temInsercaoProgramada = true
            } else {
              this.temInsercaoExibida = true
            }
          }

          if (
            (veiculacao.quantidadeFalhas && veiculacao.quantidadeFalhas > 0) ||
            (veiculacao.quantidadePendentes &&
              veiculacao.quantidadePendentes > 0)
          ) {
            this.temInsercaoFalhada = true
          }

          if (
            veiculacao.quantidadeCompensadas &&
            veiculacao.quantidadeCompensadas > 0
          ) {
            this.temInsercaoCompensada = true
          }

          if (
            veiculacao.quantidadeAbatidas &&
            veiculacao.quantidadeAbatidas > 0
          ) {
            this.temInsercaoAbatida = true
          }
        }
      })

      return program
    })

    if (primeiraVeiculacao) {
      this.data = primeiraVeiculacao
      this.primeiraVeiculacao = primeiraVeiculacao
    }

    this.botaoComprarEnabled = this.temProgramasVeiculados(this.programs)

    if (!this.isTelaAcompanhamento && !this.posVendaMode) {
      this.validarInsercoesSugeridas()
    }

    this.formatarVeiculacoesProgramas()

    this.configureDataTable(this.programs)

    this.totalInsercoes()

    this.getDiasDoMes(this.data)
  }

  totalInsercoes() {
    this.insercoesTotais = 0

    this.programs.forEach(program => {
      program.veiculacoes.forEach(veiculacao => {
        this.insercoesTotais += veiculacao.quantidadeInsercoes
      })
    })
  }

  temProgramasVeiculados(programas: any[]): boolean {
    let programasVeiculados = 0

    programas.forEach(programa => {
      const diaDoMesInsercao = Object.values(programa.diaDoMesInsercao)
      let quantidade

      if (diaDoMesInsercao) {
        quantidade = diaDoMesInsercao.reduce((p: number, n: number) => p + n, 0)
      }

      programasVeiculados += quantidade
    })

    return programasVeiculados > 0
  }

  private validarInsercoesSugeridas() {
    const errosValidacao = []
    let errosMuitasInsercoesPorDia = false
    let itemMensagens = {}

    this.programs.forEach(program => {
      let totalInsercoes: number = 0

      program.veiculacoes.forEach(veiculacao => {
        totalInsercoes += veiculacao.quantidadeInsercoes

        if (veiculacao.quantidadeInsercoes > 3) {
          errosMuitasInsercoesPorDia = true
        }
      })

      if (program.quantidade > totalInsercoes) {
        errosValidacao.push({
          nomePrograma: program.nomePrograma,
          mePrograma: program.mePrograma,
          quantidadeComprada: program.quantidade,
          quantidadeInsercoes: totalInsercoes,
        })
      }
    })

    if (errosValidacao.length > 0) {
      this.servicesLoading.setService({
        name: SERVICE_NAMES.ALTERAR_DATA,
        loading: true,
      })

      this.simulacaoV2Service
        .getSugestaoData(this.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe(response => {
          this.servicesLoading.setService({
            name: SERVICE_NAMES.ALTERAR_DATA,
            loading: false,
          })

          this.dataInicio = response.dataInicio

          this.dataFim = response.dataFim

          itemMensagens = {
            step1: errosValidacao,
            step2: `${this.transformDateToLayout(
              response.dataInicio,
            )} a ${this.transformDateToLayout(response.dataFim)}`,
          }

          if (!this.transformDateToLayout(response.dataInicio)) {
            if (response.programasIndisponiveis.length > 0) {
              this.dialogHelper.openModalProgramaIndisponivel(
                response.programasIndisponiveis,
                null,
                result => {
                  if (
                    result ===
                    BtnDialogRevisarPlanoValueActions.btnValues.REVISAR_PROGRAMA
                  ) {
                    this.store.dispatch(
                      UnavailableProgramnsAction.changeValue(
                        'programasIndisponiveis',
                        response.programasIndisponiveis,
                      ),
                    )
                    this.redirectPlanoProgramIndisponivel(
                      result,
                      errosValidacao,
                    )
                  }
                },
              )
            }
          } else {
            const data =
              this.dataToDialog.setDataDialogRevisarPlanoEData(itemMensagens)

            this.dialogHelper.openModalRevisarPlanoData(
              data,
              errosValidacao,
              result => {
                if (
                  result.action ===
                  BtnDialogRevisarPlanoValueActions.actions.REDIRECT
                ) {
                  const stateData = this.state.getValue().simulator.data

                  this.servicesLoading.setService({
                    name: SERVICE_NAMES.REGERAR_PROGRAMAS,
                    loading: true,
                  })

                  if (
                    result.value ===
                    BtnDialogRevisarPlanoValueActions.btnValues.REVISAR_PROGRAMA
                  ) {
                    this.redirectPlanoProgramIndisponivel(
                      result.value,
                      errosValidacao,
                    )
                  } else if (
                    result.value ===
                    BtnDialogRevisarPlanoValueActions.btnValues.REVISAR_LIST_1
                  ) {
                    this.store.dispatch(
                      UnavailableProgramnsAction.changeValue(
                        'programasIndisponiveis',
                        errosValidacao,
                      ),
                    )

                    this.redirectPlanoProgramIndisponivel(
                      result.value,
                      errosValidacao,
                    )
                  }
                } else if (
                  result.action ===
                  BtnDialogRevisarPlanoValueActions.actions.APLICAR_DATA
                ) {
                  this.store.dispatch(
                    SimulatorActions.changeValue(
                      'initialDate',
                      this.dataInicio,
                    ),
                  )

                  this.store.dispatch(
                    SimulatorActions.changeValue('finalDate', this.dataFim),
                  )

                  this.servicesLoading.setService({
                    name: SERVICE_NAMES.ALTERAR_DATA,
                    loading: true,
                  })

                  this.simulacaoV2Service
                    .getSugestaoData(this.id)
                    .pipe(takeUntil(this.destroy$))
                    .subscribe(response => {
                      if (response.programasIndisponiveis.length > 0) {
                        this.dialogHelper.openModalProgramaIndisponivel(
                          response.programasIndisponiveis,
                          null,
                          result => {
                            if (
                              result ===
                              BtnDialogRevisarPlanoValueActions.btnValues
                                .REVISAR_PROGRAMA
                            ) {
                              this.redirectPlanoProgramIndisponivel(
                                result,
                                errosValidacao,
                              )
                            }
                          },
                        )
                      } else {
                        this.simulacaoService.gerarVeiculacoes(this.id)

                        this.servicesLoading.setService({
                          name: SERVICE_NAMES.ALTERAR_DATA,
                          loading: false,
                        })
                      }
                    })

                  this.simulacaoService.alterarDataSimulacao(
                    this.id,
                    this.dataInicio,
                    this.dataFim,
                  )
                }
              },
            )
          }
        })
    } else if (errosMuitasInsercoesPorDia) {
      const buttons: DefaultDialogGenericaButton[] = []

      buttons.push({ value: true, text: 'ALTERAR PLANO DE MÍDIA' })

      buttons.push({
        value: false,
        text: 'ENVIAR PLANO DE MÍDIA',
        class: 'button-branco',
      })

      const title = 'ATENÇÃO!'
      const subtitle =
        'Notamos que, pelo plano de mídia atual, seu anúncio será veiculado várias vezes em um mesmo dia. Para alcançar seu objetivo, indicamos o aumento do período de datas do anúncio.'

      this.defaultDialog.openErrorDialog(title, subtitle, {
        buttons,
        afterClosed: (result: boolean) => {
          if (result) {
            this.dialogHelper.openModalData(
              this.dataToDialog.dataToModalDate(
                this.simulacao,
                this.parametrosSLA,
              ),
            )
          }
        },
      })
    }
  }

  redirectPlanoProgramIndisponivel(parametro: string, programasInvalidos) {
    const stateData = this.state.getValue().simulator.data

    this.servicesLoading.setService({
      name: SERVICE_NAMES.REGERAR_PROGRAMAS,
      loading: true,
    })

    this.store.dispatch(SimulatorActions.setStepV2(3))
    this.store.dispatch(SimulatorActions.changeValue('id', null))

    this.advertisementStorageService.setadvertisementErrorSubject({
      id: stateData.id,
      programErrors: programasInvalidos,
    })

    this.router.navigate([`configuracao-campanha`], {
      queryParams: { id: stateData.id },
    })
  }

  transformDateToLayout(data: any) {
    if (data) {
      const datePart = data.replace('-', '/').match(/\d+/g),
        year = datePart[0].substring(2), // get only two digits
        month = datePart[1],
        day = datePart[2]

      return day + '/' + month + '/' + year
    }

    return false
  }

  public configureDataTable(newData: any[]) {
    this.dataSource = new MatTableDataSource(newData)
  }

  private getDiasDoMes(data: Date) {
    if (data !== undefined) {
      this.diasDoMes = []

      const qtdDias = new Date(
        data.getFullYear(),
        data.getMonth() + 1,
        0,
      ).getDate()

      const dias = Array.from(Array(qtdDias).keys())

      dias.forEach(dia => {
        const diaCerto = dia + 1
        const d = new Date(data.getFullYear(), data.getMonth(), diaCerto)
        const diaSemanaNome = this.diasSemana[d.getDay()]

        this.diasDoMes.push({
          numero: diaCerto,
          diaSemanaNome: diaSemanaNome,
          id: d.toString(),
          diffDataHoje: this.diffDataComHoje(d),
        })
      })

      this.columnsToDisplay = []
      this.diasDoMes.forEach(dia => this.columnsToDisplay.push(dia.id))

      this.diasDoMesCalendario = this.calendario.gerarDiasDoMes(data)
    }

    this.formatarDiasDoMes()
    this.verificarBloquearRecuarMes()
  }

  getColumnsToDisplay() {
    return ['info', ...this.columnsToDisplay]
  }

  formatarDiasDoMes() {
    this.diasDoMesCalendario = this.diasDoMesCalendario.map(dia => {
      if (!dia.numero) return dia

      const programas = this.programs

      programas.forEach(programa => {
        programa.namedVeiculacoes.forEach(veiculacao => {
          const dateString = veiculacao.date
          const date = new Date(`${dateString}T00:00`)
          if (dia.date.getTime() === date.getTime()) {
            dia.veiculado = true
          }
        })
      })

      return dia
    })
  }

  verificarBloquearRecuarMes() {
    // if (this.isTelaAcompanhamento) {
    //   this.bloquearRecuarMes = false;
    //   return;
    // }
    const primeiraVeiculacao = this.calendario.datetimeToDateMonth(
      this.primeiraVeiculacao,
    )

    const actualDate = this.calendario.datetimeToDateMonth(new Date())
    const selectDatePreviousMonth = this.calendario.datetimeToDateMonth(
      this.data,
      true,
    )

    if (this.isTelaAcompanhamento) {
      if (selectDatePreviousMonth < primeiraVeiculacao) {
        this.bloquearRecuarMes = true
      } else {
        this.bloquearRecuarMes = false
      }

      return
    }

    if (selectDatePreviousMonth < actualDate) {
      this.bloquearRecuarMes = true
    } else {
      this.bloquearRecuarMes = false
    }
  }

  avancarMes() {
    this.data = new Date(this.data.getFullYear(), this.data.getMonth() + 1, 1)
    this.getDiasDoMes(this.data)
    this.verificarBloquearRecuarMes()
  }

  recuarMes() {
    this.data = new Date(this.data.getFullYear(), this.data.getMonth() - 1, 1)
    this.getDiasDoMes(this.data)
    this.verificarBloquearRecuarMes()
  }

  private montaResultadoSimulacao(simulacao: any): ResultadoSimulacao {
    // let state = this.state.getValue().simulator.data;
    const resultado = new ResultadoSimulacao()
    resultado.programas = []

    resultado.id = simulacao.id

    simulacao.programas.forEach(prog => {
      let strDiasSemana = ''

      if (prog.diasSemana.dom === true) {
        strDiasSemana += 'DOM'
      }

      if (prog.diasSemana.seg === true) {
        strDiasSemana += 'SEG'
      }

      if (prog.diasSemana.ter === true) {
        strDiasSemana += 'TER'
      }

      if (prog.diasSemana.qua === true) {
        strDiasSemana += 'QUA'
      }

      if (prog.diasSemana.qui === true) {
        strDiasSemana += 'QUI'
      }

      if (prog.diasSemana.sex === true) {
        strDiasSemana += 'SEX'
      }

      if (prog.diasSemana.sab === true) {
        strDiasSemana += 'SAB'
      }

      const programa: ProgramaSimulacao = {
        id: prog.id,
        quantidade: prog.quantidade,
        exibidora: simulacao.meExibidora,
        cdPrograma: prog.id,
        mePrograma: prog.mePrograma,
        nomePrograma: prog.nomePrograma,
        secundagem: prog.secundagem,
        valorCusto: prog.valorCusto,
        valorTotal: prog.valorTotal,
        diasSemana: strDiasSemana,
        impacto: prog.estimatedAudience,
      }

      resultado.programas.push(programa)
    })

    return resultado
  }

  excluirPrograma(programa) {
    const indexPrograma = this.simulacao.programas.findIndex(
      prog => prog.id === programa.id,
    )

    this.simulacao.programas.splice(indexPrograma, 1)

    this.servicesLoading.setService({
      name: SERVICE_NAMES.ALTERAR_PROGRAMAS,
      loading: true,
    })

    this.simulacaoService.alterarProgramas(
      this.montaResultadoSimulacao(this.simulacao),
    )
  }

  editDates(programa) {
    const dialogConfig = this.dataToDialog.configureDialog(
      programa,
      this.data,
      this.simulacao,
      this.minDate,
      this.parametrosSLA,
    )

    const dialogRef = this.dialog.open(DialogCalendarioComponent, dialogConfig)

    dialogRef.afterClosed().subscribe(excluirPrograma => {
      if (excluirPrograma === true) {
        this.excluirPrograma(programa)
      } else {
        this.refreshResumoEPreco()
        this.getSimulacao()
        this.botaoComprarEnabled = this.temProgramasVeiculados(this.programs)
      }

      this.formatarDiasDoMes()
      this.totalInsercoes()
    })

    this.sendButonEventGTM('alterar_datas')
  }

  refreshResumoEPreco() {
    const programas = this.programs
    const programasRedux = this.formatarProgramasRedux(programas)

    this.recalcularQuantidadeProgramas()
    this.formatarVeiculacoesProgramas()

    this.editouVeiculacoes = true

    this.store.dispatch(
      SimulatorActions.changeValue('programs', programasRedux),
    )

    let valorTotal = 0

    this.programs.forEach(program => {
      valorTotal += program.valorTotal
    })

    this.totalGeral = valorTotal
  }

  formatarProgramasRedux(programas: ProgramaCalendario[]): ProgramsData[] {
    return programas.map(programa => {
      const diaDoMesInsercao = Object.values(programa.diaDoMesInsercao)
      let quantidade

      if (diaDoMesInsercao) {
        quantidade = diaDoMesInsercao.reduce((p: number, n: number) => p + n, 0)
      }

      const filteredWeekDays = Object.keys(programa.diasSemana).filter(
        dia => programa.diasSemana[dia],
      )

      const weekDays = filteredWeekDays.map(dia => dia.toUpperCase()).join(',')

      const program: ProgramsData = {
        id: undefined,
        cdProgram: null,
        name: programa.nomePrograma,
        programME: programa.mePrograma,
        picture: this.buscarImagemPrograma(programa.mePrograma),
        advertisements: quantidade ? quantidade : 0,
        weekDays,
        estimatedAudience: programa.impacto,
        price: programa.valorCusto,
      }

      return program
    })
  }

  recalcularQuantidadeProgramas() {
    this.programs.forEach(programa => {
      const diaDoMesInsercao = Object.values(programa.diaDoMesInsercao)

      programa.quantidade = diaDoMesInsercao.reduce(
        (p: number, n: number) => p + n,
        0,
      )

      programa.valorTotal = programa.quantidade * programa.valorCusto
    })
  }

  formatarVeiculacoesProgramas() {
    this.programs.forEach(programa => {
      programa.namedVeiculacoes = this.calendario.gerarNamedVeiculacoes(
        programa.diaDoMesInsercao,
      )
    })
  }

  quantidadeDias(diaDoMesIns): string {
    const diaDoMesInsercao = Object.values(diaDoMesIns)

    return (
      diaDoMesInsercao.length +
      ' dia' +
      (diaDoMesInsercao.length === 1 ? '' : 'simulatorStep')
    )
  }

  getAdvertisementNumber(programs: ProgramsData[]) {
    if (programs.length === 0) return 0

    return programs.reduce((prev, curr) => {
      return {
        ...curr,
        advertisements: prev.advertisements + curr.advertisements,
      }
    }).advertisements
  }

  buscarImagemPrograma(sigla: string) {
    return this.repositorioImagemService.buscarImagemPrograma(sigla, {
      largura: 275,
      altura: 172,
    })
  }

  footerActionClicked(event: string) {
    switch (event) {
      case FooterActions.AVANCAR:
        this.checkIsSupplyValid(this.simulacao)
        this.sendEventGTMBeginCheckout()
        break
      case FooterActions.SALVAR:
        this.salvar()
        break
      case FooterActions.VOLTAR:
        this.voltar()
        break
      default:
        break
    }
  }

  buildExitConfirmationModal(): MatDialogConfig {
    const title = 'Ver meus vídeos'
    const subtitle =
      'Ao voltar à página de vídeos, este plano de exibição será refeito.'

    const buttons: DefaultDialogGenericaButton[] = [
      {
        text: 'CONTINUAR EM DATAS DO ANÚNCIO',
        value: false,
        class: 'button-azul-auto',
      },
      {
        text: 'VOLTAR À PÁGINA DE VÍDEOS',
        value: true,
        class: 'button-branco',
      },
    ]

    const dialogConfig = new MatDialogConfig()
    dialogConfig.disableClose = false
    dialogConfig.autoFocus = true
    dialogConfig.panelClass = 'dialogAlert'
    dialogConfig.width = '400px'
    dialogConfig.height = '500px'
    dialogConfig.data = new DialogAvisoData(
      title,
      TipoAviso.Custom,
      null,
      null,
      null,
      TipoIcone.Atencao,
      subtitle,
      null,
      buttons,
    )

    return dialogConfig
  }

  private handleApproved = () => {
    const state: AppState = this.state.getValue()
    const data = state.simulator.data

    if (data.fluxoCompra === FluxoCompraEnum.NORMAL) {
      this.store.dispatch(SimulatorActions.setIsPaymentEnabled(true))
      this.simulatorFlow.nextScreen(this.simulatorRoutes.resumoCampanha)
    } else {
      this.confirmarReserva(data)
    }
  }

  advance() {
    if (this.userType === UserType.LONGTAIL) {
      const isMaster = localStorage.getItem('isMaster')

      isMaster === 'true'
        ? this.biometryHelper.handleBiometryStatus(this.biometry_status)
        : this.biometryHelper.handleInviteStatus(this.inviteStatus)
    } else {
      this.handleApproved()
    }
  }

  public confirmarReserva(data): void {
    // confirmar veiculação de programas
    this.dialog
      .open(DialogConfirmarDatasAnuncioComponent, {
        width: '496px',
        height: '512px',
      })
      .afterClosed()
      .subscribe(response => {
        if (response) {
          this.servicesLoading.setService({
            name: SERVICE_NAMES.CONFIRMAR_RESERVA,
            loading: true,
          })

          this.simulacaoService.confirmarReserva(data.id)
        }
      })
  }

  voltar() {
    this.store.dispatch(SimulatorActions.setStepV2(5))
    const simuladorData = this.state.getValue().simulator.data

    this.router.navigate(
      [`simulacao/video-anuncio/${simuladorData.exhibitorCode}`],
      { queryParams: { id: simuladorData.id } },
    )
  }

  async salvar() {
    const data = this.state.getValue().simulator.data
    const authUser = JSON.parse(localStorage.getItem('user-data-string'))
    const nomeUsuario = authUser.nome_usuario

    this.servicesLoading.setService({
      name: SERVICE_NAMES.ASSOCIAR_SIMULACAO,
      loading: true,
    })

    this.simulacaoService
      .associarSimulacaoNomeUsuario(data.id, nomeUsuario, true)
      .subscribe(_ => {
        this.router.navigate(['/'])
        this.servicesLoading.setService({
          name: SERVICE_NAMES.ASSOCIAR_SIMULACAO,
          loading: true,
        })
      })
  }

  limparSimulacao() {
    this.store.dispatch(SimulatorActions.setInitialState())
  }

  recalcularDatas() {
    const botoes: any[] = []
    botoes.push({ value: 'S', text: 'SIM, QUERO UMA NOVA SUGESTÃO DE DATAS' })
    botoes.push({
      value: 'N',
      text: 'NÃO, MANTENHA MEU PLANO COMO ESTÁ',
      class: 'button-branco',
    })

    this.dialogHelper.openModal(
      this.dataToDialog.modalDataGenerico(
        'VOCÊ TEM CERTEZA?',
        'Ao confirmar, as alterações realizadas manualmente serão perdidas.',
        TipoIcone.Atencao,
        botoes,
      ),
      null,
      resultModal => {
        if (resultModal === 'S') {
          this.servicesLoading.setService({
            name: SERVICE_NAMES.GERAR_VEICULACOES,
            loading: true,
          })
          this.simulacaoService.gerarVeiculacoes(this.id)
        }
      },
    )
  }

  get isTelaAcompanhamento(): boolean {
    return this.simulacao.estadoCampanha === 'EM_ANDAMENTO'
  }

  exibirFiltros() {
    this.exibirFiltrosRecomendacao = true
  }

  filtrarRecomendacao() {
    this.servicesLoading.setService({
      name: SERVICE_NAMES.FILTRAR_RECOMENDACAO,
      loading: true,
    })

    const state = this.state.getValue().simulator.data

    const id = state.id
    const dataInicio = state.initialDate.split('-').reverse().join('-')
    const dataFim = state.finalDate.split('-').reverse().join('-')
    const tipoExibicao = this.tipoExibicaoSelecionada
    const quantidadeMaxima = this.exibicoesMaximasSelecionada

    const props = new FiltroRecomendacao({
      dataInicio,
      dataFim,
      tipoExibicao,
      quantidadeMaxima,
    })

    this.simulacaoService.filtrarRecomendacao(id, props)
    this.sendButonEventGTM('alterar_recomendacao_personalizado')
  }

  private hideChangeDateBtns(): void {
    console.log(this.simulacao.estadoCampAposPagamento)

    if (
      (this.simulacao.estadoCampAposPagamento ===
        EstadoCampanhaEnum.EM_ANDAMENTO ||
        this.simulacao.estadoCampAposPagamento ===
          EstadoCampanhaEnum.AGUARDANDO_DATAS) &&
      this.simulacao.estadoCampanha === EstadoCampanhaEnum.EM_ANDAMENTO &&
      !!this.simulacao.pagamentoSimulacaoControle &&
      this.simulacao.pagamentoSimulacaoControle.status === 1
    ) {
      this.hideChangeDate = true
    }
  }

  getUserInfo() {
    const permissions = this.jwtHelper.parseJwtFromLocalStorage()

    this.userControlService.userInfo$.pipe(first()).subscribe(res => {
      this.userType = res.userType
      this.biometry_status = res.biometryStatus
      if (this.userType === UserType.LONGTAIL) {
        this.getInviteStatus()
      }
    })
  }

  getInviteStatus() {
    this.inviteStatus$ = this.invitationService.getInvitationStatus()
    this.inviteStatus$.subscribe(status => {
      this.inviteStatus = status
    })
  }

  checkIsSupplyValid(simulacao) {
    // if (!simulacao.videoOfertaValida && simulacao.videoControle.tipoVideo == PaymentInversionVideoType.PLAYER_VATI) {
    //    return this.notificationService.dateSupplyValidVati(simulacao)
    // } else{
    this.advance()
    // }
  }

  getDataUser() {
    this.userControlService.userInfo$.pipe(first()).subscribe(response => {
      this.user = response
    })
  }

  sendButonEventGTM(buttonName) {
    this.gtmHandlerService.buttonClickEvent(
      this.user,
      buttonName,
      true,
      'plano_de_midia_personalizado/datas',
    )
  }

  setDataGTM(itemsBeginCheckout) {
    if (itemsBeginCheckout) {
      this.beginCheckoutGTMData.items = [
        {
          item_id: 'período_de_veiculacao',
          item_name: 'plano_de_middia_personalizado',
          affiliation: itemsBeginCheckout.meExibidora,
          index: 0,
          item_brand: 'gsim',
          item_category: tipoExperienciaGTMValue(
            itemsBeginCheckout.tipoExperiencia,
          ),
          item_category2: itemsBeginCheckout.municipio,
          item_variant: itemsBeginCheckout.secundagem,
          ad_goal: snakeCase(itemsBeginCheckout.objetivoAlcanceCampanha),
          currence: 'R$',
          item_list_name: itemsBeginCheckout.programas.map(item =>
            snakeCase(item.nomePrograma),
          ),
          value: itemsBeginCheckout.valorTotal,
          price: itemsBeginCheckout.valorTotal,
        },
      ]
    }
  }

  sendEventGTMBeginCheckout() {
    this.gtmHandlerService.beginCheckoutEvent(this.beginCheckoutGTMData)
  }

  reloadCampaignPostSales(id: string) {
    this.id = parseInt(id)
    this.getSimulacao(true, true)
    this.edicao = false
    this.getDiasDoMes(this.data)
    this.getSLAs()
  }

  closeBanner() {
    const banner = document.getElementById('discountBanner')
    banner.classList.add('opacity-0')

    setTimeout(() => {
      banner.classList.add('hidden')
    }, 300)
  }
  getDiscount() {
    const queryParams = this.activeRouter.snapshot.queryParams
    const discountParam = queryParams['incentivizedDiscount'] as boolean

    if (discountParam) {
      this.hasIncentivizedDiscount = discountParam
    } else {
      this.dicountIncentivizedService
        .getDiscountIncentivized()
        .subscribe(hasDicount => {
          this.hasIncentivizedDiscount = hasDicount
        })
    }
    this.hasIncentivizedDiscount === true
      ? (this.messgeInfoPriceChange =
          'Houve alteração de preço em um ou mais programas após a alteração do período de exibição.')
      : (this.messgeInfoPriceChange = this.messgeInfoPriceChange)
  }
}
