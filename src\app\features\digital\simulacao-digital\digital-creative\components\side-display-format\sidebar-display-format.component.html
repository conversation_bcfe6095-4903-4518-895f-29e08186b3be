<div>
  <app-slide-card
    class="rounded-t-2xl rounded-bl-2xl"
    [manualToggle]="isOpen"
    [nonSingleton]="true"
  >
    <div class="flex w-full h-screen">
      <div class="min-h-screen min-w-5 lg:min-w-0 lg:h-0 bg-black/60"></div>

      <div
        class="flex flex-col relative w-full h-full max-w-[calc(100%-21px)] lg:max-w-full min-h-0"
      >
        <div
          class="size-10 bg-neutral-600 absolute -left-5 cursor-pointer rounded-full flex items-center justify-center"
          (click)="closeSidebar()"
        >
          <mat-icon
            class="material-symbols-rounded !size-auto text-[30px] text-white"
          >
            close
          </mat-icon>
        </div>

        <div class="flex flex-col pt-8 pb-6 px-4 lg:px-6 bg-white">
          <adtech-typography
            variant="label"
            size="small"
            styles="!text-content-secondary-light"
            >Formato de criativo</adtech-typography
          >
          <adtech-typography
            variant="title"
            size="medium"
            as="div"
            styles="text-content-primary-light"
            >{{ displayFormat?.title }}</adtech-typography
          >
        </div>

        <div
          class="flex-grow overflow-y-auto max-h-[calc(100vh-75px)] pb-8 pt-4 lg:pt-6 min-h-0 bg-background-secondary-light"
        >
          <div class="px-4 lg:px-6">
            <div
              class="h-[164px] overflow-hidden flex items-center justify-center w-full rounded-2xl bg-overlay-luminosity-negative_1-light"
            >
              <img [src]="displayFormat?.imgUrl" [alt]="displayFormat?.title" />
            </div>
          </div>

          <div class="w-full px-4 py-6 lg:px-6 text-center">
            <adtech-typography variant="body" size="small">
              Seu criativo deve seguir as especificações técnicas
              <br />correspondentes a esse formato.
            </adtech-typography>
          </div>

          <div class="mt-6 px-4 lg:px-6" *ngIf="isNotSpecialFormat()">
            <div *ngIf="settings?.SHOW_BUTTON_INIT_GERADOR">
              <div
                class="advise-gerador"
                data-element="button"
                data-state="activated"
                data-area="envio_criativo"
                data-section="formatos_display"
                data-label="gerar_criativo"
                [ngClass]="{ 'disabled-ia': this.checkDisabledIa() }"
              >
                <div class="banner">
                  <div class="content-title">
                    <div class="icon">
                      <mat-icon svgIcon="ai-create"></mat-icon>
                    </div>
                    <div class="text">Crie o seu banner com a IA da Globo.</div>
                    <div class="tag">Novo</div>
                  </div>
                  <div>
                    Experimente o novo Gerador de Criativos e crie anúncios
                    incríveis com IA!
                  </div>
                  <div class="content-button">
                    <button
                      glbButton
                      class="c-dialog-display-format__btn"
                      (click)="openModalGenerateCreative()"
                    >
                      Teste Agora
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex flex-col px-4 lg:px-6">
            <div
              class="flex justify-between items-center py-4 border-b border-neutral-200"
            >
              <adtech-typography
                variant="body"
                size="small"
                styles="!text-content-secondary-light"
                >dimensão</adtech-typography
              >
              <adtech-typography variant="body" size="small" as="div">{{
                displayFormat?.dimension
              }}</adtech-typography>
            </div>
            <div
              class="flex justify-between items-center py-4 border-b border-neutral-200"
            >
              <adtech-typography
                variant="body"
                size="small"
                styles="!text-content-secondary-light"
                >peso</adtech-typography
              >
              <adtech-typography variant="body" size="small" as="div">{{
                displayFormat?.weight
              }}</adtech-typography>
            </div>
            <div
              class="flex justify-between items-center py-4 border-b border-neutral-200"
            >
              <adtech-typography
                variant="body"
                size="small"
                styles="!text-content-secondary-light"
                >extensão</adtech-typography
              >
              <adtech-typography variant="body" size="small" as="div">{{
                displayFormat?.files
              }}</adtech-typography>
            </div>
            <div
              class="flex flex-col py-4 gap-4 border-b border-neutral-200"
              *ngIf="formatParam === formatDigitalSelected.IN_STREAM_VIDEO"
            >
              <adtech-typography
                variant="body"
                size="small"
                styles="!text-content-secondary-light"
                >duração</adtech-typography
              >
              <div class="flex flex-col gap-4">
                <app-video-duration-info duration="6">
                  Vídeos de 6 segundos aparecem <strong>ao longo</strong> da
                  mídia e não podem ser pulados pelo público.
                </app-video-duration-info>

                <app-video-duration-info duration="15">
                  Vídeos de 7 a 15 segundos aparecem <strong>no início</strong> da mídia e não
                  podem ser pulados pelo público.
                </app-video-duration-info>

                <app-video-duration-info duration="180">
                  Vídeos de 16 a 180 segundos aparecem <strong>no início</strong> da mídia e
                  podem ser pulados pelo público.
                </app-video-duration-info>
              </div>
            </div>
          </div>

          <div class="mt-6 px-4 lg:px-6">
            <div
              class="flex flex-col p-4 items-start justify-center w-full rounded-2xl bg-overlay-luminosity-negative_1-light"
            >
              <div class="flex gap-2">
                <mat-icon class="material-symbols-rounded">warning</mat-icon>
                <adtech-typography variant="title" size="small"
                  >Atenção</adtech-typography
                >
              </div>
              <adtech-typography variant="body" size="small">
                O seu criativo será validado de acordo com as especificações
                técnicas e as Políticas de conteúdo.
              </adtech-typography>
            </div>
          </div>
        </div>

        <div
          class="flex flex-col md:flex-row items-center gap-2 justify-end p-4 bg-white"
        >
          <button
            glbButton
            (click)="viewPreview(displayFormat?.tag)"
            theme="purple-gradient-secondary"
          >
            Ver preview
          </button>
          <ng-container
            *ngIf="
              formatParam === formatDigitalSelected.CAROUSEL ||
                formatParam === formatDigitalSelected.ESPECIAL_PUBLICITARIO;
              else simpleUploadWithoutModal
            "
          >
            <button
              glbButton
              class="c-dialog-display-format__btn"
              (click)="openSelectedFormatModal()"
            >
              adicionar criativo
            </button>
          </ng-container>
          <ng-template #simpleUploadWithoutModal>
            <app-creative-sended
              class="w-full md:w-auto"
              [showListCreatives]="false"
              (click)="closeSidebar()"
              customClassBtn="purple-gradient"
              themeBtn="purple-gradient"
              isGalleryVersion="'true'"
            ></app-creative-sended>
          </ng-template>
        </div>
      </div>
    </div>
  </app-slide-card>
</div>

<app-blackout-screen
  *ngIf="isOpen"
  (onClickInside)="closeSidebar()"
></app-blackout-screen>
