<div class="gerador-form-container d-flex flex-column">
  <ng-container *ngIf="inputAtts?.type === 'text'">
    <label class="form-label" [for]="inputAtts.label">
      {{ inputAtts.label }}
    </label>
    <input
      [name]="inputAtts.label"
      [type]="inputAtts.type"
      [placeholder]="inputAtts.placeholder"
      (input)="handleChange($event.target.value)"
      [(ngModel)]="inputAtts.value"
      data-element="input_text"
      data-state="activated"
      data-area="gerador_criativos"
      [attr.data-section]="inputAtts.label"
      [attr.data-label]="inputAtts.label"
      [attr.data-keyword]="inputAtts.value ?? ''"
    />
  </ng-container>

  <ng-container *ngIf="inputAtts?.type === 'select'" data-element="select">
    <label
      class="form-label"
      [for]="inputAtts.label"
      data-element="select"
      data-state="activated"
      data-area="gerador_criativos"
      [attr.data-section]="inputAtts.label"
      [attr.data-label]="inputAtts.label"
      [attr.data-keyword]="selectedKeyword ?? ''"
    >
      {{ inputAtts.label }}
    </label>
    <el-select
      class="custom-select"
      [placeholder]="inputAtts.placeholder"
      [model]="inputAtts.value"
      (modelChange)="handleChange($event, inputAtts)"
    >
      <el-option
        *ngFor="let item of inputAtts.options"
        [label]="item.title"
        [elDisabled]="item.elDisabled"
        [value]="item.title"
      >
      </el-option>
    </el-select>

    <div class="d-flex flex-column optional-input" *ngIf="showDescriptionInput">
      <label class="form-label" [for]="subOptionDatas?.title">
        {{ subOptionDatas?.title }}
      </label>
      <input
        [name]="subOptionDatas?.title"
        type="text"
        [placeholder]="subOptionDatas?.placeholder"
        (input)="handleSubOptionChange($event.target.value)"
        [(ngModel)]="subOptionDatas.value"
        data-element="input_text"
        data-state="activated"
        data-area="gerador_criativos"
        [attr.data-section]="inputAtts.label"
        [attr.data-label]="inputAtts.label"
        [attr.data-keyword]="selectedKeyword ?? ''"
      />
    </div>
  </ng-container>

  <ng-container *ngIf="inputAtts.type === 'file'">
    <ng-container>
      <ng-content select="dinamic-error-box"></ng-content>
    </ng-container>
    <label class="form-label" [for]="inputAtts.label">
      {{ inputAtts.label }}
    </label>
    <app-gerador-custom-file
      [inputAtts]="inputAtts"
      (onInput)="handleChange($event)"
      [currentStep]="currentStep"
    ></app-gerador-custom-file>
  </ng-container>

  <ng-container *ngIf="inputAtts.type === 'textarea'">
    <label class="form-label" [for]="inputAtts.label">
      {{ inputAtts.label }}
    </label>
    <textarea
      [name]="inputAtts.label"
      (input)="handleChange($event.target.value)"
      [(ngModel)]="inputAtts.value"
      [placeholder]="inputAtts.placeholder"
      data-element="textarea"
      data-state="activated"
      data-area="gerador_criativos"
      [attr.data-section]="inputAtts.label"
      [attr.data-label]="inputAtts.label"
      [attr.data-keyword]="inputAtts.value ?? ''"
    ></textarea>
  </ng-container>
</div>
