@import '_colors';
@import '_breakpoints';

.c-dialog-display-format {
  position: fixed;
  top: 0;
  right: -100%;
  width: 540px;
  height: 100%;
  background-color: $color-white;
  transition: right 0.3s ease;
  z-index: 1000;
  overflow-y: scroll;

  &__header {
    display: flex;
    flex-direction: column;
    background-color: $color-black-sec;
    background-image: url('../../../../../../../../src/assets/imgs/digital/creative/formats/header-dialog-format.svg');
    background-position: top -8px right -7px;
    background-repeat: no-repeat;
    background-size: 140px;
    width: 100%;
    height: 160px;
    padding: 1.25rem 1.5rem;
    margin-bottom: 2rem;
    font-family: GlobotipoTexto-Regular;

    @include break-below(xs) {
      background-size: 100px;
      height: auto;
      padding: 1rem;
    }
  }

  &__title {
    font-size: 2rem;
    font-weight: 600;
    line-height: 44px;
    letter-spacing: -0.2px;
    font-family: GlobotipoTexto-Regular;
    color: $color-medium-graphite;
    margin: 0;

    @include break-below(xs) {
      font-size: 1.6rem;
    }
  }

  &__subtitle {
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: -0.2px;
    color: $color-light-graphite;
    font-family: GlobotipoTexto-Regular;
    margin: 0;
    margin-top: 12px;
  }

  &__btn-back {
    display: flex;
    flex-direction: row;
    gap: 12px;
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    color: $color-medium-graphite;
    cursor: pointer;
    align-items: center;

    @include break-below(xs) {
      font-size: 1rem;
    }
  }

  &__content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 4.5rem;

    @include break-below(xs) {
      padding: 1rem;
      margin-bottom: 7rem;
      overflow: hidden;
    }
  }

  &__image-format {
    display: flex;
    flex-direction: column;
    margin-bottom: 2rem;

    & > * {
      text-align: center;
      align-self: center;
      width: 100%;
    }

    @include break-below(xs) {
      margin-bottom: 1rem;
    }
  }

  &__description-format {
    max-width: 400px;
    margin: 0;
    margin-top: 2rem;
    color: $color-dark-graphite;
    font-family: GlobotipoTexto-Regular;
  }

  &__info,
  &__alert {
    background-color: $color-background-grey;
    padding: 1.5rem 1rem;
    border-radius: 12px;
    width: 100%;

    @include break-below(xs) {
      padding: 1rem;
    }
  }

  &__config-list {
    list-style-type: none;
    margin: 0;
    padding: 0;

    li {
      padding: 1rem 0;
      color: $color-medium-graphite;
      font-family: GlobotipoTexto-Regular;

      display: flex;
      align-items: center;
      gap: 8px;

      span {
        font-weight: bold;
        color: $color-dark-graphite;
      }

      &:not(:last-child) {
        border-bottom: 1px dashed $color-border-elements;
      }

      &:last-child {
        padding-bottom: 0;
      }

      &:first-child {
        padding-top: 0;
      }

      @include break-below(xs) {
        padding: 0.5rem 0;
      }
    }
  }

  &__alert {
    margin: 2rem 0 1rem;
    border: 1px dashed $color-border-elements;

    &-title {
      margin: 0;
      display: flex;
      font-size: 1rem;
      align-items: flex-end;
      color: $color-medium-graphite;
      font-family: GlobotipoTexto-Regular;
    }

    &-icon {
      margin-right: 8px;
    }

    &-desc {
      margin: 0;
      padding-top: 1rem;
      font-size: 1rem;
      color: $color-medium-graphite;
      font-family: GlobotipoTexto-Regular;
    }

    @include break-below(xs) {
      margin: 1rem 0;
    }
  }

  &__actions {
    position: fixed;
    bottom: 0;
    width: inherit;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: flex-end;
    background-color: $color-background-grey;
    border: 1px solid $color-border-elements;
    gap: 16px;

    @include break-below(xs) {
      display: block;
      padding: 1rem;
    }
  }

  &__btn {
    width:auto;

    &:focus {
      outline: none;
    }

    &:first-of-type {
      @include break-below(xs) {
        width:100%;
        margin-bottom: 0.5rem;
      }
    }

    @include break-below(xs) {
      width: 100%;
      padding: 0.5rem 0;
    }
  }

  &__file {
    display: none;
  }

  &.open {
    right: 0;
  }

  @include break-below(xs) {
    width: 100%;
  }
}

::ng-deep #send-files {
  height: auto !important;
  width: 100% !important;

  @include break-above(xs) {
    width: auto !important;
  }
}

.spacing-top-sections {
  margin-top: 32px;
}

.content-disclaimer {
  background-color: $color-background-grey;
  padding: 1.5rem 1rem;
  border-radius: 12px;
  width: 100%;
  margin-bottom: 32px;
  margin-top: 16px;
  font-size: 1rem;
  color: $color-medium-graphite;
  font-family: GlobotipoTexto-Regular;
  display: flex;
  justify-content: center;
}

.detail-logomarca {
  span {
    font-weight: 400;
  }
}
.size-16 {
  font-size: 16px;
}
.advise-gerador{
  width: 100%;
  margin-bottom: 2rem;
  height: 174px;
  cursor: auto;
   .banner {
     display: flex;
     flex-direction: column;
     justify-content: space-between;
     background-color: #E6F7FF;
     border-radius: 8px;
     padding: 16px 16px;
     width: 100%;
     gap: 8px;
     position: relative;
 }
 
 .text {
   display: flex;
   align-items: center;
   justify-content: space-between;
   background: linear-gradient(to right, #0079fd, #2d51fb, #5a29fa, #8800f8);
   -webkit-background-clip: text;
   -webkit-text-fill-color: transparent;
   font-size: 16px;
   font-weight: bold;
   font-family: GlobotipoTexto-Regular;
   font-weight: 700;
   flex-grow: 1;
   margin-left: 12px;
 
 }
 .banner .icon {
     display: flex;
     align-items: center;
     justify-content: center;
     width: 40px;
     height: 40px;
     background: linear-gradient(#0079fd, #2d51fb, #5a29fa, #8800f8);;
     border-radius: 8px;
     color: white;
     font-size: 20px;
 }
 
 .banner .tag {
   position: absolute;
   top: -10px;
   right: -10px;
   background: linear-gradient(to right, #0079fd, #2d51fb, #5a29fa, #8800f8);
   color: white;
   font-size: 12px;
   font-weight: bold;
   padding: 4px 8px;
   border-radius: 12px;
   text-transform: capitalize;
 } 
 
 .content-title{
  display: flex;
 }
 .content-button{
  display: flex;
  justify-content: flex-end;

 }
 }
 .disabled-ia {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}