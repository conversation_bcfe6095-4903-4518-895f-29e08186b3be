/* eslint-disable @typescript-eslint/no-unused-vars */
import { ImpactsWithInvestment } from '@app/features/digital/simulacao-digital/oferta-digital/utils/oferta-digital.models'
import { HttpClient, HttpHeaders, HttpResponse } from '@angular/common/http'
import { Injectable } from '@angular/core'
import {
  CreativePreviewData,
  AssociarCriativo,
  AssociarLinkCriativo,
  ChangeCreativeReproved,
  DeletarCriativo,
  EnvioCriativo,
  UploadImage,
} from '@app/shared/models/digital-sender'
import { environment } from '@env/environment'
import { catchError } from 'rxjs/operators'
import { AuthHttpClient } from '../../auth-http-client/auth-http-client.service'
import { ServiceHelper } from '../serviceHelper'
import { BehaviorSubject, Observable } from 'rxjs'
import {
  ImpactsWithEstimatedPublic,
  ImpressionsByValueOrSlider,
} from '@app/features/digital/simulacao-digital/oferta-digital/utils/oferta-digital.models'
import { CreativeSenderPayload } from '@app/shared/models/creative-details'
import { DigitalCampaign } from '@app/shared/models/digital-campaign'
import { ResponsePaginado } from '@app/shared/models/response-paginado'
import { FeedNotificacao } from '@app/features/pos-venda/models/feed-notificacao'
import { CPM } from '@app/shared/models/cpm'
import { MotivoReprovacao } from '@app/features/globo-interno/opec-digital/components/avaliar-url/motivo-reprovacao'
import { AvaliacaoOpecUrlData } from '@app/features/globo-interno/opec-digital/components/avaliar-url/avaliacao-opec-url-data'
import { EmailNotification } from '../../../../shared/models/digital-sender'
import { StatusDigitalCampaign } from '@app/features/digital/my-orders-digital/data-model/const-variable'
import { DigitalCampanha } from '@app/store/digital/digital.model'
import {
  CreativeHistory,
  CreativeItem,
  CreativeListResponse,
  CreativeUsageHistory,
  ZipDetail,
} from './model/digital-interface-utils'

@Injectable({
  providedIn: 'root',
})
export class DigitalService {
  constructor(
    private http: HttpClient,
    private authHttp: AuthHttpClient,
    private serviceHelper: ServiceHelper,
  ) {}
  // Consumo de endpoints do digital
  private linkCreative = new BehaviorSubject('')
  private planningCampaign = new BehaviorSubject<
    ResponsePaginado<DigitalCampaign>
  >(null)
  private approvingCampaign = new BehaviorSubject<
    ResponsePaginado<DigitalCampaign>
  >(null)
  private onAirCampaign = new BehaviorSubject<
    ResponsePaginado<DigitalCampaign>
  >(null)
  private finishedCampaign = new BehaviorSubject<
    ResponsePaginado<DigitalCampaign>
  >(null)

  private feedNotificacao = new BehaviorSubject<FeedNotificacao[]>(null)

  private selectedMaterialListSource = new BehaviorSubject<CreativeItem[]>([])
  selectedMaterialList$ = this.selectedMaterialListSource.asObservable()

  get linkCreative$() {
    return this.linkCreative.asObservable()
  }

  get planningCampaign$() {
    return this.planningCampaign.asObservable()
  }

  get approvingCampaign$() {
    return this.approvingCampaign.asObservable()
  }

  get onAirCampaign$() {
    return this.onAirCampaign.asObservable()
  }

  get finishedCampaign$() {
    return this.finishedCampaign.asObservable()
  }

  get FeedNotificacao$() {
    return this.feedNotificacao.asObservable()
  }

  setLinkCreative(url: string) {
    this.linkCreative.next(url)
  }

  getDigitalFormats() {
    return this.authHttp
      .get(`${environment.api.digitalFormats}`)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'getDigitalFormats',
            null,
          ),
        ),
      )
  }

  updateSelectedMaterialList(list: CreativeItem[]): void {
    this.selectedMaterialListSource.next(list)
  }

  getCreatives(
    status?:
      | 'APROVADO'
      | 'CONTEUDO_APROVADO'
      | 'PENDENTE'
      | 'CONTEUDO_PENDENTE'
      | 'REPROVADO'
      | 'CONTEUDO_REPROVADO',
    format = null,
    period?:
      | 'TODAY'
      | 'LAST_7DAYS'
      | 'LAST_30DAYS'
      | 'LAST_60DAYS'
      | 'LAST_90DAYS'
      | 'LAST_6MONTHS',
    page = 0,
    pageSize: number = 11,
  ) {
    let queryParams = ''
    queryParams += `?page=${page}`
    if (pageSize) {
      queryParams += `&size=${pageSize}`
    }

    if (status) {
      queryParams += `&status=${status}`
    }

    if (period) {
      queryParams += `&period=${period}`
    }

    if (format) {
      queryParams += `&format=${format}`
    }

    return this.authHttp
      .get<
        ResponsePaginado<CreativeListResponse>
      >(`${environment.api.getCreatives}${queryParams}&orderBy=id&direction=desc`)
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'getCreatives',
            {},
            false,
          )
          throw e
        }),
      )
  }

  getCreativeStatusHistory(id: string) {
    return this.authHttp
      .get<
        CreativeHistory[]
      >(`${environment.api.getCreativeStatusHistory.replace('{externalId}', id)}`)
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'getCreativeStatusHistory',
            {},
            false,
          )
          throw e
        }),
      )
  }
  getCreativeStatusHistoryFromExternal(id: string) {
    return this.authHttp
      .get<
        CreativeHistory[]
      >(`${environment.api.getCreativeStatusHistoryFromExternal.replace('{externalId}', id)}`)
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'getCreativeStatusHistoryFromExternal',
            {},
            false,
          )
          throw e
        }),
      )
  }

  getCreativeUsage(id: string) {
    return this.authHttp
      .get<
        CreativeUsageHistory[]
      >(`${environment.api.getCreativeUsageHistory.replace('{idCriativo}', id)}`)
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'getCreativeUsage',
            {},
            false,
          )
          throw e
        }),
      )
  }

  deleteCreatives(requestIds: Array<string>) {
    return this.authHttp
      .post<ResponsePaginado<CreativeListResponse>>(
        `${environment.api.deleteCreatives}`,
        {
          requestId: requestIds,
        },
      )
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'deleteCreatives',
            null,
          ),
        ),
      )
  }

  getPreSignUrl(nomeArquivo: string, tipoArquivo: string) {
    return this.authHttp
      .get(
        `
      ${environment.api.preSignUrl
        .replace('{nome_do_arquivo}', encodeURIComponent(nomeArquivo))
        .replace('{tipo_do_arquivo}', tipoArquivo)}`,
      )
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'getPreSignUrl',
            null,
          ),
        ),
      )
  }
  postImageUpload(params: UploadImage) {
    return this.authHttp
      .post(environment.api.uploadDigitalImage, params, {
        reportProgress: true,
        observe: 'events',
      })
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'postImageUpload',
            null,
          ),
        ),
      )
  }
  gerarCampanhaDigital(params: any) {
    return this.authHttp
      .post(environment.api.gerarCampanhaDigital, params)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'postGerarCampanhaDigital',
            null,
          ),
        ),
      )
  }
  atualizarCampanhaDigitalCPM(id, params: any) {
    return this.authHttp
      .patch(
        environment.api.atualizarCampanhaDigital.replace('{idCampanha}', id),
        params,
      )
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'atualizarCampanhaDigital',
            null,
          ),
        ),
      )
  }
  digitalCampaingUpdate(id, params: any) {
    return this.authHttp
      .put(
        environment.api.atualizarCampanhaDigital.replace('{idCampanha}', id),
        params,
      )
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'digitalCampaingUpdate',
            {},
            false,
          )
          throw e
        }),
      )
  }
  getCreativeInformation(requestId: string) {
    return this.authHttp
      .get(
        environment.api.creativeInformationManagement.replace(
          '{requestId}',
          requestId,
        ),
      )
      .pipe(
        catchError(error => {
          this.authHttp.handleError(
            this.constructor.name,
            'creativeInformationManagement',
            {},
            false,
          )
          throw error
        }),
      )
  }

  envioCriativo(params: EnvioCriativo) {
    return this.authHttp
      .post(environment.api.envioCriativo, params)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'postEnvioCriativo',
            null,
          ),
        ),
      )
  }
  associarCriativoCampanha(params: AssociarCriativo) {
    return this.authHttp
      .post(environment.api.associarCriativoNaCampanha, params)
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'associarCriativoCampanha',
            {},
            false,
          )
          throw e
        }),
      )
  }
  recuperarCampanhaDigital(id: string) {
    if (!id) return
    return this.authHttp
      .get(
        environment.api.recuperarCampanhaDigitalId.replace('{idCampanha}', id),
      )
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'recuperarCampanhaDigital',
            null,
          ),
        ),
      )
  }
  deletarCriativo(params: DeletarCriativo) {
    return this.authHttp
      .put(
        environment.api.deletarCriativo
          .replace('{idCampanha}', params.campanhaId.toString())
          .replace('{idCriativo}', params.criativoId.toString()),
        null,
      )
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'deletarCriativo',
            {},
            false,
          )
          throw e
        }),
      )
  }

  getPreviewImagemCoordenadas(
    formato: string,
    mnemonico: string,
    plataforma: string,
  ) {
    const url = environment.api.getPreviewImagemCoordenadas
      .replace('{formato}', formato)
      .replace('{mnemonico}', mnemonico)
      .replace('{plataforma}', plataforma)
    return this.authHttp
      .get<CreativePreviewData>(url)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'getPreviewImagemCoordenadas',
            null,
          ),
        ),
      )
  }
  atualizarCampanhaUrl(idCampanha: string, link: AssociarLinkCriativo) {
    return this.authHttp
      .put(
        environment.api.atualizarLinkCampanha.replace(
          '{idCampanha}',
          idCampanha,
        ),
        link,
      )
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'atualizarCampanhaUrl',
            [],
            false,
          )
          throw e
        }),
      )
  }
  consultarAvaliacaoUrl(token: string) {
    return this.authHttp
      .get(environment.api.avaliarUrlCampanhaOpec + '/' + token)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'atualizarCampanhaUrl',
            null,
          ),
        ),
      )
  }
  getMotivosReprovacaoUrl() {
    return this.authHttp
      .get<
        MotivoReprovacao[]
      >(environment.api.avaliarUrlCampanhaOpec + '/motivos')
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'getFeedNotificacoes',
            null,
          ),
        ),
      )
  }
  avaliarUrlCampanha(body: AvaliacaoOpecUrlData) {
    return this.authHttp
      .put(environment.api.avaliarUrlCampanhaOpec, body)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'getFeedNotificacoes',
            null,
          ),
        ),
      )
  }
  impactsWithEstimatedPublic(params: ImpactsWithEstimatedPublic) {
    return this.authHttp
      .post(environment.api.impressoesImpactos, params)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'impactsWithEstimatedPublic',
            null,
          ),
        ),
      )
  }
  impactsWithInvestment(params: ImpactsWithInvestment) {
    return this.authHttp
      .post(environment.api.impressoesImpactos, params)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'impactsWithInvestment',
            null,
          ),
        ),
      )
  }
  getCreativeDetails(requestId: string) {
    const url = environment.api.creativeInformationManagement.replace(
      '{requestId}',
      requestId,
    )
    return this.authHttp.get<CreativeSenderPayload>(url).pipe(
      catchError(error => {
        this.authHttp.handleError(
          this.constructor.name,
          'getCreativeDetails',
          {},
          false,
        )
        throw error
      }),
    )
  }

  getDigitalCampanha(
    status: StatusDigitalCampaign[],
    page: number,
    pageSize: number = 9,
    sort = '',
  ) {
    const url = environment.api.getCampaignStatusByCategory.replace(
      '{categoria}',
      status.toString(),
    )
    let queryParams = ''
    if (page) {
      queryParams += `&page=${page}`
    }
    if (pageSize) {
      queryParams += `&size=${pageSize}`
    }
    queryParams += `&sort=dataCriacao,desc`
    return this.authHttp
      .get<ResponsePaginado<DigitalCampaign>>(`${url}?${queryParams}`)
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'campanhasDigital',
            {},
            false,
          )
          throw e
        }),
      )
  }

  getFeedNotification(): Observable<FeedNotificacao[]> {
    return this.authHttp
      .get<FeedNotificacao[]>(environment.api.getDigitalFeed)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'getFeedNotificacoes',
            null,
          ),
        ),
      )
  }
  getFeedNotificationCriativos(id: number): Observable<FeedNotificacao[]> {
    return this.authHttp
      .get<
        FeedNotificacao[]
      >(environment.api.getDigitalFeedPorCriativos.replace('{id}', id.toString()))
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'getFeedNotificacoesCriativos',
            null,
          ),
        ),
      )
  }

  removeFeedNotificacao(id: number): Observable<any> {
    return this.authHttp
      .put(
        environment.api.removeFeedNotificacao.replace('{id}', id.toString()),
        null,
      )
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'removeFeedNotificacao',
            null,
          ),
        ),
      )
  }

  getCPM(): Observable<CPM[]> {
    return this.authHttp
      .get<CPM[]>(`${environment.api.getCPM}`)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'getCreativeDetails',
            null,
          ),
        ),
      )
  }

  sendFileToCompress(file: File): Observable<any> {
    const formData = new FormData()
    formData.append('files[]', file)
    const headers_object = new HttpHeaders()
    headers_object.append('Content-Type', 'multipart/form-data')
    const httpOptions = {
      headers: headers_object,
    }
    return this.http
      .post(environment.api.imageCompress, formData, httpOptions)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'sendFileToCompress',
            null,
          ),
        ),
      )
  }

  saveDigitalUrl(id: number, url: string) {
    return this.authHttp
      .put(
        environment.api.saveDigitalUrl.replace('{idCampanha}', id.toString()),
        {
          url: url,
        },
      )
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'saveDigitalUrl',
            null,
          ),
        ),
      )
  }

  pauseCreative(creativeId: string, lineItemId: string) {
    return this.authHttp.post(environment.api.pauseCreative, {
      creativeId: creativeId,
      lineItemId: lineItemId,
    })
  }

  activateCreative(creativeId: string, lineItemId: string) {
    return this.authHttp.post(environment.api.activateCreative, {
      creativeId: creativeId,
      lineItemId: lineItemId,
    })
  }

  estenderCampanha(idCampanha: number) {
    return this.authHttp.post(
      environment.api.estenderCampanha.replace(
        '{idCampanha}',
        idCampanha.toString(),
      ),
      {},
    )
  }

  // Novos endpoints da tela de alcance
  impactsBySlider(params: ImpressionsByValueOrSlider) {
    return this.authHttp
      .post(environment.api.impactsBySlider, params)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'impactsBySlider',
            null,
          ),
        ),
      )
  }

  impactsByValue(params: ImpressionsByValueOrSlider) {
    return this.authHttp
      .post(environment.api.impactsByValue, params)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'impactsByValue',
            null,
          ),
        ),
      )
  }

  postUserPhoneNumber(params) {
    const user = localStorage.getItem('user-data-string')
    const userName = JSON.parse(user).nome_usuario
    return this.authHttp
      .post(
        environment.api.postPaymentTelephone.replace('{username}', userName),
        params,
      )
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'postUserPhoneNumber',
            {},
            false,
          )
          throw e
        }),
      )
  }

  recoverDigitalCampaign(id: string) {
    return this.authHttp
      .get<DigitalCampanha>(
        environment.api.recuperarCampanhaDigitalId.replace('{idCampanha}', id),
      )
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'recuperarCampanhaDigital',
            {},
            false,
          )
          throw e
        }),
      )
  }
  emailSendControl(body: EmailNotification) {
    return this.authHttp.put(environment.api.idSendEmailControl, body).pipe(
      catchError(e => {
        this.authHttp.handleError(
          this.constructor.name,
          'emailSendControl',
          {},
          false,
        )
        throw e
      }),
    )
  }
  changeCreativeEasReproved(params: ChangeCreativeReproved) {
    return this.authHttp
      .put(
        environment.api.changeCreativeWasReproved
          .replace('{idCampanha}', params.idCampaign)
          .replace('{idCriativoSubstituido}', params.idOldCreative)
          .replace('{idCriativoNovo}', params.idNewCreative),
        null,
      )
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'changeCreativeEasReproved',
            {},
            false,
          )
          throw e
        }),
      )
  }

  deleteDigitalCampaign(idCampanha: string) {
    return this.authHttp
      .delete(
        environment.api.deleteDigitalDraft.replace('{idCampanha}', idCampanha),
      )
      .pipe(
        catchError(e => {
          this.serviceHelper.handleError(
            this.constructor.name,
            'deleteDigitalCampaign',
            null,
          )
          throw e
        }),
      )
  }

  processRepayment(idCampanha: number) {
    return this.authHttp
      .post(
        environment.api.estornarDigital.replace(
          '{idCampanha}',
          idCampanha.toString(),
        ),
        {},
      )
      .pipe(
        catchError(e => {
          this.authHttp.handleError(
            this.constructor.name,
            'estornarAnuncioDigital',
            {},
            false,
          )
          throw e
        }),
      )
  }
  getZipDetail(externalId: string) {
    const url = environment.api.getZipCreative.replace(
      '{externalID}',
      externalId,
    )
    return this.authHttp
      .get<ZipDetail[]>(url)
      .pipe(
        catchError(
          this.serviceHelper.handleError(
            this.constructor.name,
            'getZipDetail',
            null,
          ),
        ),
      )
  }
  getFileSize(url: string): Observable<number> {
    return new Observable(observer => {
      this.http
        .get(url, { observe: 'response', responseType: 'blob' })
        .subscribe({
          next: (response: HttpResponse<Blob>) => {
            const contentLength = response.headers.get('Content-Length')
            if (contentLength) {
              observer.next(+contentLength)
            } else if (response.body) {
              observer.next(response.body.size)
            } else {
              observer.error('Não foi possível obter o tamanho do arquivo.')
            }
            observer.complete()
          },
          error: error => {
            observer.error('Erro na requisição: ' + error.message)
          },
        })
    })
  }
}
