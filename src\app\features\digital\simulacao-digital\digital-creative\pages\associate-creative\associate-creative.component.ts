/* eslint-disable @typescript-eslint/no-unused-vars */
import { Component, OnInit, ViewChild } from '@angular/core'
import { Location } from '@angular/common'
import { DigitalService } from '@app/core/services/no-auth/digital/digital.service'
import { isWebUri } from 'valid-url'
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog'
import { ControlExpandableService } from '@app/core/services/control-expandable/control-expandable.service'
import { DialogBuilderUrlComponent } from '../../components/dialog-builder-url/dialog-builder-url.component'
import { ResponsePaginado } from '@app/shared/models/response-paginado'
import {
  CreativeItem,
  CreativeListResponse,
} from '@app/core/services/no-auth/digital/model/digital-interface-utils'
import { MultiSelectData } from '@app/shared/components/multi-select-check/model/multi-select-data'
import { ImageService } from '@app/features/digital/creative-manager/services/image.service'
import { finalize, first, takeUntil } from 'rxjs/operators'
import { addOrRemoveFromArrayOfObjects } from '@app/shared/utils/array.helper'
import { ActivatedRoute, Router } from '@angular/router'
import { DigitalHelper } from '@app/shared/utils/digital-helper'
import { DigitalCampanha } from '@app/store/digital/digital.model'
import { Observable, ReplaySubject } from 'rxjs'
import { AppState } from '@app/store/app.model'
import { Store } from '@ngrx/store'
import { NotificationService } from '@app/core/services/notification/notification.service'
import { ERROR_DIALOG_HTML } from '@app/shared/utils/swal-templates'
import { AssociarCriativo } from '@app/shared/models/digital-sender'
import { DialogConfirmChangeCreativeComponent } from '../../components/dialog-confirm-change-creative/dialog-confirm-change-creative.component'
import { MatDialogConfig } from '@angular/material/dialog'
import {
  CreativeSenderPayload,
  CreativeSenderResponse,
} from '@app/shared/models/creative-details'
import { GeradorCriativosService } from '@app/features/adstudio/services/gerador-criativos.service'
import { DialogEscolhaEnvioCriativo } from '../../components/dialogs/dialog-escolha-envio-criativo/dialog-escolha-envio-criativo.component'
import { SendCreativeService } from '@app/features/adstudio/services/send-creative.service'
import { CreativeService } from '@app/core/services/creative-service/creative-service.service'
import { TechnicalValidationCreativeStatus } from '@app/shared/models/status-criativo-digital'
import { DigitalActions } from '@app/store/digital/digital.actions'
import { DigitalSenderHelper } from '@app/shared/utils/digital-sender-helper'
import { SendFilesComponent } from '../../components/send-files/send-files.component'
import { GloboHeaderSettings } from '@app/shared/components/globo-header/model/globo-header-settings'
import {
  RemoteConfigs,
  RemoteConfigsService,
} from '@app/core/services/remote-configs/remote-configs.service'
import { formatTypes } from '@app/features/digital/creative-manager/utils/dictionary'

@Component({
  selector: 'app-associate-creative',
  templateUrl: './associate-creative.component.html',
  styleUrls: ['./associate-creative.component.scss'],
})
export class AssociateCreativeComponent implements OnInit {
  approvedCreativeList = new ResponsePaginado<CreativeListResponse>()
  selectedMaterialList: Array<CreativeItem> = []
  searchTerm: string = ''
  destroy$ = new ReplaySubject()
  isLoadingCreatives: boolean = false
  managementCreative: boolean = false

  reprovedCreative: CreativeSenderPayload

  adaptReprovedCreative: Partial<CreativeItem>

  digitalData$: Observable<DigitalCampanha>
  digitalData: DigitalCampanha
  digitalDataInfo: DigitalCampanha

  isPaymentPending = false
  changeCreative: boolean = false
  dialogConfig = new MatDialogConfig()

  idDigitalCampaign: string
  changeCreativeId: number = 0

  creativeDetails: CreativeSenderPayload[]
  detailsImageStorage: File
  typeImage: string
  imageUrl: string
  private isUploading: boolean = false
  loopObject = {}
  messageError: string
  statusCriativo: boolean
  typeMsg: number
  originAdsStudio: boolean = false
  @ViewChild(SendFilesComponent) sendFilesComponent: SendFilesComponent
  page = 0
  size = 4
  format: any
  managementFile: any
  isGalleryVersion: any
  imageHeigth: number
  imageWidth: number
  dialogHelper: any
  featureFlags: RemoteConfigs

  get headerConfig(): GloboHeaderSettings {
    return {
      headerTitle: 'nome do anúncio',
      handlePrevious: () => this.handleBack(),
      pageTitle: 'Selecione os criativos do anúncio',
    }
  }

  formatOptions: MultiSelectData[] = [
    {
      name: 'carousel',
      value: 'Carrossel',
      selected: false,
    },
    {
      name: 'half-page',
      value: 'Meia Página',
      selected: false,
    },
    {
      name: 'billboard',
      value: 'Billboard',
      selected: false,
    },
    {
      name: 'retângulo médio',
      value: 'Retangulo medio',
      selected: false,
    },
    {
      name: 'in-stream vídeo',
      value:
        'Banner vídeo - billboard,Banner vídeo - retângulo,Banner Video,In-stream vídeo,Globo DAI - In Stream Video',
      selected: false,
    },
    {
      name: 'especial publicitário',
      value: 'Chamada Especial Publicitário',
      selected: false,
    },
    {
      name: 'superleaderboard',
      value: 'Superleaderboard',
      selected: false,
    },
    {
      name: 'maxiboard',
      value: 'Maxiboard',
      selected: false,
    },
    {
      name: 'todos os formatos',
      value: null,
      selected: true,
    },
  ]
  link: string = ''
  disabled: boolean = true
  linkError: boolean = false

  private campaignEncriptedData: string
  private decodedCampaignObject

  constructor(
    private location: Location,
    private digitalService: DigitalService,
    private dialog: MatDialog,
    private controlSection: ControlExpandableService,
    private imageService: ImageService,
    public router: Router,
    private digitalHelper: DigitalHelper,
    protected store: Store<AppState>,
    private notificationService: NotificationService,
    private activatedRoute: ActivatedRoute,
    private geradorCriativoService: GeradorCriativosService,
    private creativeIaService: SendCreativeService,
    private creativeService: CreativeService,
    private digitalSenderHelper: DigitalSenderHelper,
    private remoteConfigService: RemoteConfigsService,
  ) {
    this.digitalData$ = store.select((state: AppState) => state.digital)
    this.digitalData$
      .pipe(takeUntil(this.destroy$))
      .subscribe((digitalData: DigitalCampanha) => {
        this.digitalDataInfo = digitalData
      })

    this.digitalService.linkCreative$.subscribe(link => {
      this.validateSend()
      this.link = link
    })
    this.digitalService.selectedMaterialList$.subscribe(list => {
      this.validateSend()
      this.selectedMaterialList = list
    })
    this.getConfigRemote('globosim', 'WEB', 'GENERAL-VIEW')
  }

  ngOnInit(): void {
    this.changeCreative = this.router.url.includes('troca')

    if (this.changeCreative) {
      const campaignData = JSON.parse(
        atob(this.activatedRoute.snapshot.paramMap.get('campaignObject')),
      )
      this.idDigitalCampaign = campaignData['campanhaId']
    } else {
      this.idDigitalCampaign = this.activatedRoute.snapshot.paramMap.get('id')
    }
    this.getDataCampaign()
    this.getCreatives(false, false)

    this.digitalService.linkCreative$.subscribe(link => {
      this.link = link
      this.validateSend()
    })
    this.creativeIaService.uploadResult$.subscribe(result => {
      if (result && result.file) {
        this.uploadIaCreative(result.file)
      }
    })
  }

  // pega id do criativo reprovado pela url
  getChangeCreativeIdFromUrl() {
    if (this.changeCreative) {
      this.campaignEncriptedData =
        this.activatedRoute.snapshot.paramMap.get('campaignObject')
      this.decodedCampaignObject = JSON.parse(atob(this.campaignEncriptedData))
      this.idDigitalCampaign = this.decodedCampaignObject['campanhaId']
      this.changeCreativeId = this.decodedCampaignObject['criativoId']
      this.findCreativeToChange(this.digitalData?.listCriativo)
    }
  }

  // encontra criativo a ser trocado na lista de criativos da campanha
  findCreativeToChange(listaCriativos) {
    if (this.changeCreativeId) {
      this.reprovedCreative = listaCriativos?.find(
        item => item.id === this.changeCreativeId,
      )
      this.adaptReprovedCreative = this.reprovedCreative
        ? this.transformToCreativeItem(this.reprovedCreative)
        : null
      if (!this.reprovedCreative) {
        console.error('Criativo não encontrado.')
      }
    } else {
      console.error('Criativo ID não encontrado na URL.')
    }
    return null
  }

  handleBack() {
    this.location.back()
  }

  // transforma o criativo reprovado de tipo CreativeDetails para CreativeItem
  transformToCreativeItem(
    details: CreativeSenderPayload,
  ): Partial<CreativeItem> {
    return {
      id: details.id,
      format: details.format || '',
      name: details.name,
      status: details.status || '',
      s3Url: details.s3Url,
      // zipDetail: Array.isArray(details.zipDetail)
      //   ? details.zipDetail
      //   : [details.zipDetail],
    }
  }

  // recupera dados da campanha
  getDataCampaign() {
    if (this.idDigitalCampaign) {
      this.digitalService
        .recuperarCampanhaDigital(this.idDigitalCampaign)
        .subscribe(
          response => {
            this.digitalData = response
            this.changeCreative && this.getChangeCreativeIdFromUrl()
          },
          () => {
            this.notificationService
              .globoSimCommon(
                'Ops, parece que tivemos um imprevisto.',
                ERROR_DIALOG_HTML,
                'ENTENDI',
              )
              .then(() => {
                this.router.navigate(['/meus-pedidos'], {
                  queryParams: { tab: 'digital' },
                })
              })
          },
        )
    } else {
      this.notificationService.globoSimCommon(
        'Id da campanha não encontrado na URL.',
        ERROR_DIALOG_HTML,
        'ENTENDI',
      )
    }
  }

  // carregamento da listagem de criativos
  getCreatives(addMore: boolean, addLess: boolean, clear = false) {
    this.isLoadingCreatives = true
    if (clear) {
      this.approvedCreativeList = new ResponsePaginado<CreativeListResponse>()
      this.selectedMaterialList = []
    }
    if (addMore) {
      this.size = this.size + 4
    }
    if (addLess) {
      this.size = this.size - 4
    }

    this.digitalService
      .getCreatives(
        'APROVADO', // só pegará criativos aprovados
        this.formatOptions.find(format => format.selected).value,
        'LAST_6MONTHS',
        this.page,
        this.size,
      )
      .pipe(first())
      .subscribe(
        (creativeListResponse: ResponsePaginado<CreativeListResponse>) => {
          const contentHolder: ResponsePaginado<CreativeListResponse> =
            creativeListResponse
          this.findZipDetails(contentHolder)
        },
        () => {
          this.isLoadingCreatives = false
        },
      )
  }

  // carregamento da listagem de criativos (zip)
  findZipDetails(creativeListResponse: ResponsePaginado<CreativeListResponse>) {
    this.imageService
      .findZipAndSetImages(creativeListResponse.content)
      .pipe(
        first(),
        finalize(() => (this.isLoadingCreatives = false)),
      )
      .subscribe(
        updatedList => {
          creativeListResponse.content = updatedList
          this.approvedCreativeList = creativeListResponse
        },
        error => {
          console.error('Erro ao carregar detalhes do zip:', error)
        },
      )
  }

  // pesquisa por texto do criativo
  handleSearch(searchTerm: string): void {
    this.searchTerm = searchTerm.trim().toLowerCase()
    this.approvedCreativeList.content =
      this.approvedCreativeList.content.filter(
        (creative: CreativeListResponse) =>
          creative.name?.toLowerCase().includes(this.searchTerm),
      )
    if (!this.searchTerm) {
      this.getCreatives(false, true)
    }
  }

  // filtros de pesquisa de criativo
  handleFilterChange(newFilters: MultiSelectData[]) {
    this.formatOptions = newFilters
    this.getCreatives(false, true)
  }

  // quando seleciona um criativo da listagem
  handleSelectClick(event: CreativeItem) {
    this.selectedMaterialList = addOrRemoveFromArrayOfObjects(
      this.selectedMaterialList,
      event,
      'requestId',
    )
    // Envia a atualização de seleção para o serviço
    this.digitalService.updateSelectedMaterialList(this.selectedMaterialList)
  }

  // verifica um criativo deselecionado
  isCreativeDeselected(event: CreativeItem): boolean {
    return !this.selectedMaterialList.some(
      item => item.requestId === event.requestId,
    )
  }

  // envio de criativos - botão
  onClickAbrirUploadCriativos() {
    if (this.featureFlags?.SHOW_BUTTON_INIT_GERADOR) {
      this.openModalEscolha()
    } else {
      localStorage.setItem('isFromAssociation', JSON.stringify(true))
      window.open('digital/galeria/envio', '_blank')
    }
  }

  openModalEscolha(): void {
    this.dialog
      .open(DialogEscolhaEnvioCriativo)
      .afterClosed()
      .subscribe((response: number | null) => {
        if (response === 1) {
          localStorage.setItem('isFromAssociation', JSON.stringify(true))
          window.open('digital/galeria/envio', '_blank')
        } else if (response === 2) {
          this.geradorCriativoService.handlePanelState(true)
        }
        // Se response for null, não faz nada e não chama o método de estado do painel
      })
  }

  // INICIO - URL
  openBuilderUrl(event: Event) {
    this.dialogConfig.panelClass = ['url-builder', 'gtm-element-visibility']
    const dialogRef = this.dialog.open(
      DialogBuilderUrlComponent,
      this.dialogConfig,
    )

    dialogRef.afterOpened().subscribe(() => {
      const dialogElement = document.querySelector(
        '.url-builder',
      ) as HTMLElement
      if (dialogElement) {
        dialogElement.setAttribute('data-element', 'modal')
        dialogElement.setAttribute('data-state', 'viewed')
        dialogElement.setAttribute('data-area', 'associacao_criativos')
        dialogElement.setAttribute('data-section', 'url_destino')
        dialogElement.setAttribute('data-label', 'url_para_o_google_analytics')
      }
    })
    dialogRef.afterClosed().subscribe(() => {
      if (
        this.selectedMaterialList?.length >= 1 &&
        this.link !== '' &&
        this.link !== undefined
      ) {
        this.disabled = false
      }
    })
  }

  onSubmit(event: SubmitEvent): string {
    const target = event.target as HTMLFormElement
    const linkValue = (target.elements.namedItem('link') as HTMLInputElement)
      .value
    return linkValue
  }
  returnStyle(url) {
    return `
      background: url(${url}) no-repeat;
      background-size: cover;
      width: 100%;
    `
  }

  sendLink(event) {
    let linkData = event

    if (!event.includes('https://')) {
      linkData = 'https://' + event
    }

    if (!isWebUri(linkData)) {
      this.link = null
      this.digitalService.setLinkCreative(null)
      this.disabled = true
      this.linkError = true
      this.controlSection.setSectionChecked('add-url', false)
    } else {
      this.link = linkData
      this.digitalService.setLinkCreative(linkData)
      this.disabled = false
      this.linkError = false
    }
  }

  // FIM - URL

  async atualizarUrl(linkCreative) {
    const digitalDataAtualizaco = {
      campanhaUrl: linkCreative,
    }
    const id = this.idDigitalCampaign
    await this.digitalService
      .atualizarCampanhaUrl(id, digitalDataAtualizaco)
      .pipe(first())
      .subscribe()
  }

  validateSend() {
    const isLinkEmpty = this.link === '' || this.link === undefined

    if (!this.changeCreative) {
      if (this.selectedMaterialList?.length >= 5) {
        this.disabled = true
      } else if (this.selectedMaterialList?.length === 0) {
        this.disabled = isLinkEmpty
        if (!isLinkEmpty) {
          this.controlSection.setSectionChecked('add-url', true)
        } else {
          this.controlSection.setSectionChecked('add-url', false)
        }
      } else {
        this.disabled = isLinkEmpty
      }
    } else {
      this.disabled = this.selectedMaterialList?.length === 0
    }
  }

  async handleSend() {
    if (
      this.selectedMaterialList?.length > 0 &&
      this.selectedMaterialList?.length <= 5 &&
      this.link !== undefined &&
      this.link !== null
    ) {
      if (this.changeCreative) {
        const dialogConfig = new MatDialogConfig()
        dialogConfig.width = '540px'
        dialogConfig.panelClass = [
          'dialog-confirm-change-creative',
          'gtm-element-visibility',
        ]

        const dialogRef = this.dialog.open(
          DialogConfirmChangeCreativeComponent,
          dialogConfig,
        )
        dialogRef.afterOpened().subscribe(() => {
          const dialogElement = document.querySelector(
            '.dialog-confirm-change-creative',
          ) as HTMLElement
          if (dialogElement) {
            dialogElement.setAttribute('data-element', 'modal')
            dialogElement.setAttribute('data-state', 'viewed')
            dialogElement.setAttribute('data-area', 'substituicao_criativos')
            dialogElement.setAttribute(
              'data-section',
              'criativo_a_ser_substituido',
            )
            dialogElement.setAttribute('data-label', 'substituicao_de_criativo')
          }
        })
        dialogRef.afterClosed().subscribe(confirmed => {
          if (confirmed) {
            const newCreative = this.selectedMaterialList[0]
            this.deleteAndAssociateNewCreative(newCreative)
            this.digitalHelper.getDataCampaign(this.digitalData.id).then()
            this.router.navigate([
              `/digital/anuncio-digital/gerenciamento-anuncio/${this.digitalData.id}`,
            ])
          }
        })
      } else {
        this.connectCreativeCampaign()
        this.atualizarUrl(this.link)
        this.router.navigate([
          `digital/criativo/sucesso-criativo/${this.idDigitalCampaign}`,
        ])
      }
    } else {
      this.disabled = true
    }
  }

  // vincular criativo e campanha
  connectCreativeCampaign() {
    this.selectedMaterialList.forEach(creative => {
      const prospAssociate: AssociarCriativo = {
        campanhaId: this.idDigitalCampaign,
        criativoId: parseInt(creative.externalId),
      }
      this.digitalService.associarCriativoCampanha(prospAssociate).subscribe(
        () => {},
        () => {
          console.error('Erro ao associar o criativo.')
        },
      )
    })
  }

  // para troca de criativo, deleta o antigo e substitui pelo novo
  deleteAndAssociateNewCreative(newCreative) {
    const params = {
      idCampaign: this.idDigitalCampaign,
      idNewCreative: newCreative.externalId,
      idOldCreative: this.changeCreativeId.toString(),
    }
    this.digitalService
      .changeCreativeEasReproved(params)
      .subscribe(response => {
        if (response) {
          if (this.digitalData.statusPagamento) {
            this.notificationSendEmailControl(
              parseInt(this.idDigitalCampaign),
              this.changeCreativeId,
            )
          }
        }
      })
  }

  notificationSendEmailControl(idCampaing: number, idCreative: number) {
    this.digitalService.emailSendControl({
      idCampanha: idCampaing,
      idCriativo: idCreative,
    })
  }

  // adição da parte de envio de criativo para o gerador

  sendFileS3(file: File) {
    this.sendFileS3AndValidate(file)
  }
  async sendFileS3AndValidate(file: File) {
    this.detailsImageStorage = file
    try {
      if (file.type.startsWith('image/')) {
        this.typeImage = 'image'
        this.creativeService.addOrUpdateCreativeDetails([
          new CreativeSenderResponse({
            id: Math.random(),
            name: file.name,
            format: 'imagem',
            note: 'loading',
          }),
        ])
        this.digitalService.sendFileToCompress(file).subscribe(
          response => {
            if (response) {
              this.imageUrl = response.imageUrl
              this.sendCreative(this.imageUrl, file)
              return response
            }
          },
          () => {
            this.openDialogErrorGeneric()
          },
        )
        this.getDetailImage(file)
      } else {
        this.notificationService.showToastWarn(
          'O tipo de arquivo ' + file.type + ' não é permitido',
        )
      }
    } catch (error) {
      console.error('Erro durante o upload:', error)
      this.notificationService.showToastWarn(
        'Ocorreu um erro durante o upload do arquivo',
      )
    } finally {
      this.isUploading = false
    }
  }

  async sendCreative(event, url) {
    const submittedCreative = await this.digitalSenderHelper
      .sendCreative(event, url)
      .then(response => {
        if (response.id) {
          this.creativeService.setIdsPending(response.id)
          return response
        }
        this.handleError(submittedCreative.id)
      })
    ;(await this.changeCreative)
      ? this.getCreativeImage(submittedCreative.id)
      : this.getCreativeDetailsStandard(submittedCreative.id)
  }
  getCreativeImage(creativeId) {
    this.digitalService
      .getCreativeDetails(creativeId)
      .pipe(first())
      .subscribe(
        (creativeDetail: CreativeSenderPayload) => {
          const status = creativeDetail.status
          if (status !== TechnicalValidationCreativeStatus.PENDING) {
            this.setChangeCreativeDataAndValidate(
              creativeDetail,
              status,
              creativeId,
            )
          } else {
            this.loopObject[creativeId] = true
            this.creativeService.startCreativeLoop(
              (res, status, creativeId) => {
                this.setChangeCreativeDataAndValidate(res, status, creativeId)
              },
            )
          }
        },
        () => {
          this.openDialogErrorGeneric()
        },
      )
  }
  setChangeCreativeDataAndValidate(
    detalheCriativo: CreativeSenderPayload,
    status: string,
    idCriativo,
  ) {
    this.messageError = detalheCriativo.note
    this.store.dispatch(
      DigitalActions.changeValue('listCriativo', [
        { urlClickTag: '', status: '', usuario: '', ...detalheCriativo },
      ]),
    )
    this.validStatus(
      status,
      idCriativo,
      detalheCriativo.format,
      this.typeMsg,
      this.messageError,
      detalheCriativo,
    )
  }

  getCreativeDetailsStandard(creativeId) {
    this.digitalService
      .getCreativeDetails(creativeId)
      .pipe(first())
      .subscribe(
        (response: CreativeSenderPayload) => {
          const status = response.status
          if (status !== TechnicalValidationCreativeStatus.PENDING) {
            this.setStandardDataAndValidate(response, status, creativeId)
          } else {
            this.loopObject[creativeId] = true
            this.creativeService.startCreativeLoop(
              (response, status, creativeId) => {
                this.setChangeCreativeDataAndValidate(
                  response,
                  status,
                  creativeId,
                )
              },
            )
          }
        },
        () => {
          this.openDialogErrorGeneric()
        },
      )
  }
  async validStatus(
    status: string,
    id,
    tipo,
    typeMsg,
    message,
    detalhe: CreativeSenderPayload,
  ) {
    if (status === TechnicalValidationCreativeStatus.REPROVED) {
      this.typeMsg = 1
      this.messageError = detalhe.note
      if (this.changeCreative) {
        this.store.dispatch(DigitalActions.changeValue('listCriativo', []))
      }
      this.statusCriativo = false
      const message = this.checkMessageInfoCreativeReproved(this.messageError)
    }
    if (status === TechnicalValidationCreativeStatus.APPROVED) {
      const format = this.checkFormat(tipo)

      if (!format) {
        this.typeMsg = 2
        this.messageError = message
        this.statusCriativo = false
        message = 'Não foi possível obter o formato do criativo enviado'
        this.openDialogErrorSendFile(message)

        if (this.changeCreative) {
          this.store.dispatch(DigitalActions.changeValue('listCriativo', []))
        }
      } else {
        if (this.managementFile) {
          const idData = JSON.parse(sessionStorage.getItem('id_creative')) || []
          idData.push(id)
          sessionStorage.setItem('id_creative', JSON.stringify(idData))
          localStorage.setItem(
            'lastUrl',
            `digital/criativo/associar/gerenciamento/${this.idDigitalCampaign}`,
          )
        }
      }

      // exibir o modal para o upload dos criativos com origem do gerador
      if (this.originAdsStudio) {
        this.originAdsStudio = false
        this.notificationService
          .creativeAddSucesso(
            'Criativo adicionado com sucesso!',
            `Seu criativo foi adicionado e está pronto para ser associado <br> ao seu anúncio. Para exibi-lo na <strong>Galeria de Criativos</strong> e <br>torná-lo disponível para associação, atualize seus criativos.`,
            'ATUALIZAR CRIATIVOs',
          )
          .then(response => {
            if (response.isConfirmed) {
              this.getCreatives(false, false)
            }
          })
      }
    }
  }
  getDetailImage(image) {
    const URL = window.URL || window.webkitURL
    const Img = new Image()
    const filesToUpload = image
    const blob = new Blob([filesToUpload], { type: 'image' })
    Img.src = URL.createObjectURL(blob)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Img.onload = (element: any) => {
      const imgElement = element.path[0] as HTMLImageElement
      this.imageHeigth = imgElement.naturalHeight
      this.imageWidth = imgElement.naturalWidth
    }
  }

  handleError(idCreative?) {
    this.dialogHelper.closeDialog()
    this.openDialogErroTimeOutFacil(idCreative)
  }
  openDialogErroTimeOutFacil(idCreative: any) {
    throw new Error('Method not implemented.')
  }

  setStandardDataAndValidate(
    detalheCriativo: CreativeSenderPayload,
    status: string,
    idCriativo,
  ) {
    this.typeMsg = 2
    this.messageError = detalheCriativo.note
    this.validStatus(
      status,
      idCriativo,
      detalheCriativo.format,
      this.typeMsg,
      this.messageError,
      detalheCriativo,
    )
  }
  openDialogErrorGeneric() {
    {
      this.notificationService
        .globoSimCommon(
          'Ops, parece que tivemos um imprevisto.',
          ERROR_DIALOG_HTML,
          'ENTENDI',
        )
        .then(() => {
          this.router.navigate(['/meus-pedidos'], {
            queryParams: { tab: 'digital' },
          })
        })
    }
  }
  checkMessageInfoCreativeReproved(observation: string) {
    if (observation.includes('peso')) {
      return 'Identificamos que o criativo selecionado excede o peso máximo suportado para este formato. Por favor, certifique-se de que o seu criativo siga todas as especificações técnicas.'
    } else if (observation.includes('Dimensão')) {
      return 'Identificamos que o criativo selecionado não possui a dimensão/proporção correta para este formato. Por favor, certifique-se de que o seu criativo siga todas as especificações técnicas.'
    } else if (observation.includes('extensão')) {
      return 'Identificamos que o arquivo selecionado possui uma extensão que é incompatível para este formato. Por favor, certifique-se de que o seu criativo siga todas as especificações técnicas.'
    } else {
      return `Identificamos que o criativo selecionado não seguiu as especificações técnicas de <strong>peso</strong>, <strong>dimensão</strong> ou  <strong>extensão</strong> definidas para este formato. Por favor, certifique-se de que o seu criativo siga todas as especificações técnicas.`
    }
  }
  checkFormat(type) {
    this.format = type
    switch (type) {
      case formatTypes.Billboard:
      case formatTypes.RetanguloMedio:
      case formatTypes.Superleaderboard:
      case formatTypes.MeiaPagina:
      case formatTypes.Maxiboard:
      case formatTypes.InStreamVideo:
      case formatTypes.Carousel:
      case formatTypes.EspecialPublicitario:
        return true
      default:
        return false
    }
  }
  openDialogErrorSendFile(message?) {
    this.notificationService
      .errorValidateTechinicalCreative(message)
      .then(resultDialog => {
        if (resultDialog.isConfirmed) {
          this.sendFilesComponent.openFileSelector()
        } else {
          this.dialog.closeAll()
        }
      })
  }
  uploadIaCreative(image) {
    this.sendFileS3(image)
    this.originAdsStudio = true
  }

  getConfigRemote(tenant, group, scope) {
    this.remoteConfigService
      .getConfigsRemote(tenant, group, scope)
      .subscribe(data => {
        this.featureFlags = data
      })
  }
}
