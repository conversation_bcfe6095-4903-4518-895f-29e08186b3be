@import '_breakpoints';
@import '_colors';

$white: #fff;
$off-withe: #e6e6e6;
$light-gray: #d1d1d1;

@mixin ripple($color) {
  $darkenedColor: darken($color, 5%);

  &:hover {
    background: $darkenedColor
      radial-gradient(circle, transparent 1%, $darkenedColor 1%) center/15000%;
  }

  &:active {
    background-color: darken($color, 10%);
    background-size: 100%;
    transition: background 0s;
  }
}

@mixin glb-common() {
  background-image: linear-gradient(
    93deg,
    $color-heat-wave-stronger 3.54%,
    $color-orange-peel 103.33%
  );
  font-family: GlobotipoTexto-Regular;
  border-radius: 2.375rem;
  color: $white;
  font-weight: 600;
  text-transform: unset;
}

@mixin link-common() {
  font-family: 'GlobotipoTexto-Regular';
  background-color: transparent;
  border-radius: 'unset';
  border: 'unset';
  font-weight: 400;
  color: $color-primary;
  text-transform: unset;
  text-decoration: underline;
  padding: 0;
  font-size: 1rem;
}

:host {
  padding: 9px 24px;
  font-family: 'GlobotipoRounded-Bold';
  font-size: 0.875rem;
  line-height: 1.5rem;
  text-transform: uppercase;
  border-radius: 8px;
  border: none;
  box-shadow: none;
  background-position: center;
  transition: background 0.8s;
  display: flex;
  justify-content: center;
  align-items: center;

  &.primary {
    color: $white;
    background-color: $color-new-primary;
    border: 1px solid $color-new-primary;
    @include ripple($color-new-primary);
  }

  &.secondary {
    color: $color-new-primary;
    border: 1px solid $color-new-primary;
    background-color: transparent;
    @include ripple(transparent);
  }

  &.warning {
    color: $color-error-snack-letter;
    border: 1px solid $color-error-snack-letter;
    background-color: transparent;
    @include ripple(transparent);
  }

  &.another-purple-basic {
    color: $color-new-primary;
    background-color: transparent;
    @include ripple(transparent);
    padding: 0;
  }

  &.warning-basic {
    color: $color-error-snack-letter;
    background-color: transparent;
    @include ripple(transparent);
    padding: 0;
  }

  &.warning-fill {
    color: white;
    border: 1px solid $color-error-snack-letter;
    background-color: $color-error-snack-letter;
  }

  &.secondary-light {
    background-color: $white;
    color: $color-new-primary;
    border: 1px solid $color-new-primary;
    @include ripple($white);
  }
  &.secondary-light-border-grey {
    background-color: white;
    background-image: unset;
    color: $color-new-primary;
    border: 1px solid $color-athens-gray;
    gap: 12px;
  }
  &.accent {
    background-color: $color-primary;
    color: $white;
    border-radius: 8px;
    @include ripple($color-primary);
  }

  &.glb {
    @include glb-common;
    @include ripple($color-heat-wave-stronger);
  }

  &.glb-secondary {
    @include glb-common;
    background-color: transparent;
    background-image: unset;
    border: 1px solid $color-destaques-sim;
    color: $color-destaques-sim;
  }

  &.glb-secondary-light {
    @include glb-common;
    background-color: transparent;
    background-image: unset;
    border: 1px solid $color-dark-orange;
    font-weight: 400;
    color: $color-dark-orange;
    @include ripple(transparent);
  }

  &.link {
    @include link-common();
    @include ripple(transparent);
  }

  &.link-secondary {
    @include link-common();
    color: $color-dark-graphite;
    @include ripple(transparent);
  }

  &.basic {
    background-color: transparent;
    border: unset;
    color: unset;
    @include ripple($color-light-gray);
  }

  &.basic:hover {
    background-color: $color-light-gray;
  }

  &.basic-purple {
    background-color: transparent;
    border: unset;
    color: unset;
    text-transform: lowercase;
    color: $color-new-primary;
    font-weight: 600;
    font-size: 16px;
    font-family: 'GlobotipoRounded-Bold';
    padding: 0;
  }

  &.basic-purple-variation {
    background-color: transparent;
    border: unset;
    color: unset;
    text-transform: lowercase;
    color: $color-new-primary;
    font-weight: 600;
    font-size: 16px;
    font-family: 'GlobotipoRounded-Bold';
  }

  &:disabled {
    background: $off-withe;
    color: $light-gray;
    border: none;
    pointer-events: none;
  }

  &:active:not(:disabled) {
    opacity: 0.9;
  }

  &.only-letters-uppercase {
    background-color: transparent;
    border: unset;
    color: unset;
    text-transform: uppercase;
    color: $color-new-primary;
    font-weight: 600;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Bold';
    padding: 0;
  }
  @include break-below(xs) {
    width: 100%;
  }

  &.primary-purple {
    background-color: #5c43fa;
    border: unset;
    color: white;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Regular';
    padding: 0;
  }

  &.purple-transparent {
    background-color: transparent;
    border: none;
    color: #5c43fa;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Bold';
    padding: 0;
  }

  &.purple-secundary {
    background-color: transparent;
    border: 1px solid #5c43fa;
    color: #5c43fa;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Bold';
  }

  &.purple-gradient {
    background: linear-gradient(
      90deg,
      #0079fd 25%,
      #2d51fb 50%,
      #5a29fa 75%,
      #8800f8 100%
    );
    border: unset;
    color: white;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Regular';
  }

  &.purple-gradient-secondary {
    position: relative;
    display: inline-block;
    background-color: transparent;
    color: transparent;
    background-image: linear-gradient(#0079fd, #2d51fb, #5a29fa, #8800f8);
    background-clip: text;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Bold';

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: inherit;
      padding: 1px;
      background: linear-gradient(90deg, #0079fd, #2d51fb, #5a29fa, #8800f8);
      mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      mask-composite: exclude;
    }
  }

  &.purple-gradient-transparent {
    background-color: transparent;
    border: none;
    background-image: linear-gradient(
      90deg,
      #0079fd,
      #2d51fb,
      #5a29fa,
      #8800f8
    );
    color: transparent;
    background-clip: text;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Bold';
  }
  &.only-letter-gradient-flex-end {
    background: linear-gradient(90deg, #0079fd, #2d51fb, #5a29fa, #8800f8);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    background-image: linear-gradient(#0079fd, #2d51fb, #5a29fa, #8800f8);
    background-clip: text;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Bold';
    justify-content: flex-end;
    padding-right: 0;
  }
  &.only-letter-gradient-flex-center {
    background: linear-gradient(90deg, #0079fd, #2d51fb, #5a29fa, #8800f8);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Bold';
    justify-content: center;
  }
  &.purple-gradient-letter-bold {
    background: linear-gradient(
      90deg,
      #0079fd 25%,
      #2d51fb 50%,
      #5a29fa 75%,
      #8800f8 100%
    );
    border: unset;
    color: white;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Regular';
  }
  &.purple-gradient-secondary-gerador {
    display: flex;
    justify-content: center;
    position: relative;
    display: flex;
    background-color: transparent;
    color: transparent;
    background-image: linear-gradient(#0079fd, #2d51fb, #5a29fa, #8800f8);
    background-clip: text;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 14px;
    font-family: 'GlobotipoTexto-Bold';

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: inherit;
      padding: 1px;
      background: linear-gradient(90deg, #0079fd, #2d51fb, #5a29fa, #8800f8);
      mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      mask-composite: exclude;
    }
  }
}
