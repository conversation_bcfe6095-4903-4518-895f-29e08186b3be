import { Component, Input, HostBinding } from '@angular/core'

@Component({
  selector: '[glbButton]',
  templateUrl: './glb-button.component.html',
  styleUrls: ['./glb-button.component.scss'],
})
export class GloboButtonComponent {
  @HostBinding('class')
  @Input()
  theme:
    | 'primary'
    | 'secondary'
    | 'secondary-light'
    | 'basic'
    | 'accent'
    | 'warning'
    | 'warning-fill'
    | 'warning-basic'
    | 'transparent'
    | 'glb'
    | 'glb-secondary'
    | 'glb-secondary-light'
    | 'link'
    | 'basic-purple'
    | 'another-purple-basic'
    | 'basic-blue'
    | 'only-letters-uppercase'
    | 'secondary-light-border-grey'
    | 'purple-gradient-secondary'
    | 'purple-gradient'
    | 'purple-gradient-secondary'
    | 'purple-gradient-secondary-gerador'
    | 'purple-gradient-transparent'
    | 'only-letter-gradient-flex-center'
    | 'only-letter-gradient-flex-end'
    | 'purple-gradient-letter-bold'
    | 'link-secondary' = 'primary'

  constructor() {}
}
