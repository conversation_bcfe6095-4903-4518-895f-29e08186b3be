/* eslint-disable @typescript-eslint/no-unused-vars */
import { takeUntil } from 'rxjs/operators'
import { Subject, Subscription } from 'rxjs'
import { LabelValue } from '@app/shared/models/label-value'
import { SimulatorActions } from '@app/store/simulator/simulator.actions'
import { State, Store } from '@ngrx/store'
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core'
import * as moment from 'moment'
import { AppState } from '@app/store/app.model'
import { Router } from '@angular/router'
import { SimulacaoMultiexibidoraService } from '@app/core/services/no-auth/simulacao-multiexibidora/simulacao-multiexibidora.service'
import {
  CupomUtilizado,
  DataDateSelected,
} from '@app/store/simulator/simulator-data.model'
import { FiltroRecomendacao } from '@app/shared/models/filtro-recomendacao'
import { TipoExibicaoCampanha } from '@app/shared/models/tipo-exibicao-campanha'
import { SimulacaoService } from '@app/core/services/no-auth/simulacao/simulacao.service'
import { Observable } from 'rxjs-compat/Observable'
import { NotificationService } from '@app/core/services/notification/notification.service'
import {
  exclamacaoPurple,
  globoFridayCoupomText,
} from '@app/shared/utils/swal-templates'
import { TimeLineProps } from '@app/shared/models/timeline'
import { getMediaPlanSteps } from '@app/shared/utils/media-plan'

@Component({
  selector: 'app-resumo-campanha-multiexibidora',
  templateUrl: './resumo-campanha-multiexibidora.component.html',
  styleUrls: ['./resumo-campanha-multiexibidora.component.scss'],
})
export class ResumoCampanhaMultiexibidoraComponent implements OnInit {
  @Output() footerClickTocall = new EventEmitter<string>()
  @Input() footerLabelsButtons: FooterDataLabels
  @Input() footerData: FooterData

  totalGeral: number = 0
  @Input() id: number
  slasSubscription: Subscription
  destroy$ = new Subject()
  invalidPeriod = false
  diaSla: number
  loadedApi: boolean = false
  minDate: moment.Moment
  loading: boolean
  initialRange

  initialDate$: Observable<string>
  finalDate$: Observable<string>

  initialDate
  finalDate
  mediaPlan: TimeLineProps[] = []

  constructor(
    private store: Store,
    private state: State<AppState>,
    private router: Router,
    private simulacaoMultiexibidoraService: SimulacaoMultiexibidoraService,
    private simulacaoService: SimulacaoService,
    private notificationService: NotificationService,
  ) {}
  ngOnInit() {
    const state = this.state.getValue()
    this.loading = true
    this.simulacaoMultiexibidoraService.gerarVeiculacoes(this.id)
    this.store.dispatch(SimulatorActions.setStepV2(6))
    this.initialDate$ = this.store.select(
      (state: AppState) => state.simulator.data.initialDate,
    )
    this.finalDate$ = this.store.select(
      (state: AppState) => state.simulator.data.finalDate,
    )
    this.getTotalGeral()
    this.simulacaoService.getSLASimulacao(this.id)
    this.simulacaoService.getSLASimulacaoData$
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        this.diaSla = res.totalDias + 2 // regra 2D para contemplar o boleto
        this.minDate = moment().add(this.diaSla, 'days')
        this.loading = false
      })
    const tipoExperiencia = this.state.getValue().simulator.data.tipoExperiencia
    const codeExhibitor = this.state.getValue().simulator.data.exhibitorCode
    this.mediaPlan = getMediaPlanSteps(tipoExperiencia, this.id, codeExhibitor)
  }

  ngOnDestroy() {
    this.destroy$.next()
    this.destroy$.complete()
  }
  checkSupplyValid(event) {
    // const dadosSimulacao = this.state.getValue().simulator.data;
    // if (!dadosSimulacao.videoOfertaValida &&  dadosSimulacao.tipoVideo == PaymentInversionVideoType.PLAYER_VATI) {
    //  return this.notificationService.dateSupplyValidVati(dadosSimulacao)
    // } else {
    this.footerClickTocall.emit(event)
    // }
  }
  getTotalGeral() {
    const dadosSimulacao = this.state.getValue().simulator.data

    this.totalGeral = dadosSimulacao.programs.reduce((acc, programa) => {
      return acc + Number(programa.price * programa.advertisements)
    }, 0)
  }

  private validateCoupomDatelimit(startDate: string, finalDate: string) {
    const simulator = this.state.getValue().simulator.data
    if (
      simulator.pagamentoSimulacaoControle &&
      simulator.pagamentoSimulacaoControle.status === 1 &&
      simulator.cuponsUtilizados.length > 0
    ) {
      const dateObjectStart = new Date(startDate).getTime()
      const dateObjectEnd = new Date(finalDate).getTime()

      let lastInvalidVoucher: CupomUtilizado

      this.invalidPeriod =
        simulator.cuponsUtilizados.filter((r: CupomUtilizado) => {
          lastInvalidVoucher = r
          if (r.dataLimiteExibicao) {
            const formatedDate = new Date(r.dataLimiteExibicao).getTime()
            return (
              formatedDate < dateObjectStart || formatedDate < dateObjectEnd
            )
          } else {
            return false
          }
        }).length > 0

      if (this.invalidPeriod) {
        const formatedLimitDate = new Date(
          lastInvalidVoucher.dataLimiteExibicao + 'T00:00:00-0300',
        ).toLocaleDateString()
        const vouncherMessage = globoFridayCoupomText
          .replace('CODIGO_CUPOM', lastInvalidVoucher.codigoCupom)
          .replace('LIMIT_DATE', formatedLimitDate)

        this.notificationService.globoSimCommon(
          `Selecionar datas de anúncio até  ${formatedLimitDate}`,
          vouncherMessage,
          'ENTENDI',
          {
            size: 470,
            additionalClasses: {
              confirmButton: 'swal-lone-button-default--small',
            },
            customIcon: exclamacaoPurple,
          },
        )
      }
    }
  }

  changeDateRedux(dataChange: DataDateSelected) {
    this.validateCoupomDatelimit(
      dataChange.initialDateRange,
      dataChange.finalDateRange,
    )

    if (!this.invalidPeriod) {
      const initialDate = moment(dataChange.initialDateRange)
      const finalDate = moment(dataChange.finalDateRange)
      const initialDateString = moment(dataChange.initialDateRange).format(
        'DD-MM-yyyy',
      )
      const finalDateString = moment(dataChange.finalDateRange).format(
        'DD-MM-yyyy',
      )
      this.store.dispatch(
        SimulatorActions.changeValue('initialDate', initialDate),
      )
      this.store.dispatch(SimulatorActions.changeValue('finalDate', finalDate))
      this.filtrarRecomendacao(initialDateString, finalDateString)
    }
  }
  footerActionClicked(event: string) {
    this.checkSupplyValid(event)
  }
  filtrarRecomendacao(initialDateString: string, finalDateString: string) {
    const dataSimulacao: FiltroRecomendacao = {
      dataInicio: initialDateString,
      dataFim: finalDateString,
      quantidadeMaxima: 2,
      tipoExibicao: TipoExibicaoCampanha.EXIBIR_LINEAR,
    }
    this.simulacaoMultiexibidoraService
      .filtrarRecomendacao(this.id, dataSimulacao)
      .subscribe(res => {})
  }
}
export interface FooterDataLabels {
  btnVoltar: LabelValue
  btnEdicao: LabelValue
  btnAvancar: LabelValue
}

export interface FooterData {
  totalGeral: number
  edicao: boolean
  voltarVisible: boolean
  botaoComprarEnabled: boolean
}
