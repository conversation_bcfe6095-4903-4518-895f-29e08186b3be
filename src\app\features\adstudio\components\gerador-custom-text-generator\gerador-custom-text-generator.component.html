<div class="text-gen-container d-flex flex-column">
  <p>{{ inputAtts.label }}</p>

  <div *ngIf="loading; else loadedContent" class="skeleton-loader">
    <div class="skeleton skeleton-input"></div>
    <div class="flex justify-between">
      <div class="flex">
        <app-skeleton-loader
          [count]="1"
          [width]="'100%'"
          [height]="'30px'"
          [borderRadius]="'8px'"
        ></app-skeleton-loader>
        <div class="flex">
          <div class="mr-4">
            <app-skeleton-loader
              [count]="1"
              [width]="'35px'"
              [height]="'35px'"
              [borderRadius]="'8px'"
            ></app-skeleton-loader>
          </div>
          <div class="mr-4">
            <app-skeleton-loader
              [count]="1"
              [width]="'35px'"
              [height]="'35px'"
              [borderRadius]="'8px'"
            ></app-skeleton-loader>
          </div>
          <div class="mr-4">
            <app-skeleton-loader
              [count]="1"
              [width]="'35px'"
              [height]="'35px'"
              [borderRadius]="'8px'"
            ></app-skeleton-loader>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #loadedContent>
    <div
      class="input-container d-flex"
      data-element="button"
      data-state="activated"
      data-area="gerador_criativos"
      [attr.data-section]="
        currentStep.step +
        '_' +
        currentStep.component.title +
        '.' +
        inputAtts.label
      "
      [attr.data-label]="inputAtts.label"
    >
      <input
        id="text-generator"
        type="text"
        (blur)="setInfoLabelGtm($event.target.value)"
        [(ngModel)]="
          inputAtts?.control === 'mainText'
            ? adsText[currentIndex]
            : ctaButtonText[currentIndex]
        "
        (ngModelChange)="onTextChange($event, inputAtts?.control)"
        [maxLength]="inputAtts?.control === 'mainText' ? 68 : 16"
        [attr.data-label]="inputAtts?.label + '.caixa_de_texto'"
        data-element="button"
        data-state="activated"
        data-area="gerador_criativos"
        [attr.data-section]="
          currentStep.step +
          '_' +
          currentStep.component.title +
          '.' +
          currentStep.component.subtitle
        "
        [attr.data-keyword]="
          inputAtts?.control === 'mainText'
            ? adsText[currentIndex]
            : ctaButtonText[currentIndex]
        "
      />

      <div
        class="input-counter d-flex justify-content-center align-items-center"
        data-element="button"
        data-state="activated"
        data-area="gerador_criativos"
        [attr.data-section]="
          currentStep.step +
          '_' +
          currentStep.component.title +
          '.' +
          inputAtts.label
        "
        [attr.data-label]="inputAtts.label"
      >
        <span
          data-element="button"
          data-state="activated"
          data-area="gerador_criativos"
          [attr.data-section]="
            currentStep.step +
            '_' +
            currentStep.component.title +
            '.' +
            inputAtts.label
          "
          [attr.data-label]="inputAtts.label"
        >
          {{
            inputAtts?.control === 'mainText'
              ? countLetters(adsText[currentIndex]?.length, 68)
              : countLetters(ctaButtonText[currentIndex]?.length, 16)
          }}
        </span>
      </div>
    </div>
  </ng-template>

  <div>
    <div class="text-gen-controlers align-items-center">
      <div class="text-gen-action" *ngIf="!loading"></div>

      <div
        class="text-gen-page d-flex justify-content-evenly align-items-center"
        *ngIf="!loading"
        data-element="button"
        data-state="activated"
        data-area="gerador_criativos"
        [attr.data-section]="
          currentStep.step +
          '_' +
          currentStep.component.title +
          '.' +
          inputAtts.label
        "
        [attr.data-label]="inputAtts.label"
      >
        <button
          class="btn prev"
          (click)="prevPage(inputAtts?.control)"
          [disabled]="currentIndex === 0"
          data-element="button"
          data-state="activated"
          data-area="gerador_criativos"
          [attr.data-section]="
            currentStep.step +
            '_' +
            currentStep.component.title +
            '.' +
            inputAtts.label
          "
          [attr.data-label]="inputAtts.label + '.esquerda'"
          [attr.data-keyword]="'opt_' + (currentIndex + 1)"
        ></button>
        <span
          data-element="button"
          data-state="activated"
          data-area="gerador_criativos"
          [attr.data-section]="
            currentStep.step +
            '_' +
            currentStep.component.title +
            '.' +
            inputAtts.label
          "
          [attr.data-label]="inputAtts.label"
        >
          {{ currentIndex + 1 }} /
          {{
            inputAtts?.control === 'mainText'
              ? adsText.length
              : ctaButtonText.length
          }}
        </span>
        <button
          class="btn next"
          (click)="nextPage(inputAtts?.control)"
          [disabled]="
            currentIndex ===
            (inputAtts?.control === 'mainText'
              ? adsText.length
              : ctaButtonText.length) -
              1
          "
          data-element="button"
          data-state="activated"
          data-area="gerador_criativos"
          [attr.data-section]="
            currentStep.step +
            '_' +
            currentStep.component.title +
            '.' +
            inputAtts.label
          "
          [attr.data-label]="inputAtts.label + '.direita'"
        ></button>
      </div>
    </div>
  </div>
</div>
