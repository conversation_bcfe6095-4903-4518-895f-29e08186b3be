import { Component, Input, Output, EventEmitter } from '@angular/core'

@Component({
  selector: 'app-info-section',
  templateUrl: './info-section.component.html',
  styleUrls: ['./info-section.component.scss'],
})
export class InfoSectionComponent {
  @Input() title: string = '<PERSON><PERSON><PERSON><PERSON>' // Título padr<PERSON>
  @Input() description: string = 'Descrição padrão' // Descrição padrão
  @Input() buttonText: string = 'Botão de Ação' // Texto do botão padrão
  @Input() iconName: string = '' // Nome do Icone

  @Output() onClickUpload: EventEmitter<void> = new EventEmitter<void>()

  // Método que emite o evento quando o botão é clicado
  handleButtonClick() {
    this.onClickUpload.emit()
  }
}
