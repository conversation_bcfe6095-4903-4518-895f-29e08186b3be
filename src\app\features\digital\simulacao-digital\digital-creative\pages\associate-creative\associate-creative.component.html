<app-globo-header [headerSettings]="headerConfig"></app-globo-header>
<main class="px-4 md:p-10">
  <div class="max-w-[1786px] mx-auto py-4 lg:p-0">
    <adtech-typography variant="title" size="large" as="h1"
      >Selecione um ou mais criativos da sua Galeria</adtech-typography
    >
    <adtech-typography
      variant="body"
      size="medium"
      as="p"
      styles="!text-content-secondary-light"
      >Exibindo criativos adicionados e aprovados:</adtech-typography
    >
    <!-- listagem de criativos da galeria -->
    <div class="mt-4">
      <!-- filtragem -->
      <app-filters-approved-creatives
        (search)="handleSearch($event)"
        (filterChange)="handleFilterChange($event)"
        (updateCreatives)="getCreatives(false, false)"
      ></app-filters-approved-creatives>
      <!-- listagem de criativos aprovados -->
      <app-approved-creatives
        [approvedCreativeList]="approvedCreativeList"
        [isLoading]="isLoadingCreatives"
        (selectedClick)="handleSelectClick($event)"
        (moreCreatives)="getCreatives(true, false)"
        (lessCreatives)="getCreatives(false, true)"
        [selectedMaterialList]="selectedMaterialList"
        [isChangeCreative]="changeCreative"
      ></app-approved-creatives>
    </div>
    <!-- botao adicionar um criativo -->
    <div class="mt-8">
      <app-info-section
        [title]="'Precisa adicionar um criativo do seu computador?'"
        [description]="
          'Adicione um criativo na sua Galeria e selecione-o na seção acima.'
        "
        [buttonText]="'adicionar criativo na galeria'"
        [imageUrl]="'info-section'"
        (onClickUpload)="onClickAbrirUploadCriativos()"
      >
      </app-info-section>
    </div>
    <!-- divisória -->
    <div
      class="hidden lg:block mt-8 h-12 lg:border-t lg:border-border-neutral-light"
      *ngIf="selectedMaterialList.length > 0"
    >
      <div
        class="lg:ml-6 lg:mt-4 h-8 lg:border-l lg:border-border-neutral-light"
      ></div>
    </div>
    <!-- seleção de criação -->
    <div class="mt-8" *ngIf="selectedMaterialList.length > 0">
      <app-selected-creatives
        [isChangeCreative]="changeCreative"
        [reprovedCreative]="adaptReprovedCreative"
        (selectedClick)="handleSelectClick($event)"
        [creativesSelectedList]="selectedMaterialList"
      ></app-selected-creatives>
    </div>
    <!-- url criativo -->
    <div
      class="mt-8"
      *ngIf="selectedMaterialList.length > 0 && !changeCreative"
    >
      <adtech-typography
        variant="title"
        size="large"
        as="h1"
        styles="text-content-primary-light mb-4"
        >URL de destino</adtech-typography
      >

      <div class="border border-border-neutral-light rounded-lg p-4 shadow-sm">
        <form class="fiel" (ngSubmit)="onSubmit($event)">
          <adtech-typography
            variant="body"
            size="medium"
            as="p"
            styles="text-content-secondary-light mb-4"
            >Escolha para onde o seu público será levado ao clicar em seu
            anúncio. A URL de destino passará por uma análise pelo nosso time
            técnico após a
            <br />
            confirmação do pagamento. Confira nossas
            <a
              target="_blank"
              href="/digital/criativo/politicas-conteudo"
              class="text-link-default-light"
              >Políticas de conteúdo.</a
            ></adtech-typography
          >

          <div class="input-with-prefix">
            <input
              data-element="input_text"
              data-state="activated"
              data-area="associacao_criativos"
              data-section="url_destino"
              data-label="digite_url"
              [attr.data-additionalInfo]="link"
              id="ad-url"
              data-clarity-mask="True"
              name="link"
              placeholder="meusite.com.br"
              type="text"
              [(ngModel)]="link"
              class="max-w-[432px]"
              (ngModelChange)="sendLink($event)"
              globosim-input
              required
              list="example-url"
            />
          </div>
        </form>
        <app-dialog-builder-url>
          <div class="mt-2">
            <a
              target="_blank"
              (click)="openBuilderUrl($event)"
              (ngModelChange)="sendLink($event)"
              class="cursor-pointer underline text-link-default-light"
            >
              Crie uma URL para o Google Analytics
            </a>
          </div>
        </app-dialog-builder-url>
      </div>
    </div>
    <div class="mt-8">
      <footer class="footer-send-creative padding-16">
        <div class="button-field flex justify-end gap-4">
          <button
            id="button_back-details-creative"
            glbButton
            theme="purple-gradient-secondary"
            (click)="handleBack()"
            data-element="button"
            data-state="activated"
            data-area="associacao_criativos"
            data-section="finalizacao_associacao"
            data-label="voltar"
          >
            VOLTAR
          </button>
          <button
            data-element="button"
            data-state="activated"
            [attr.data-area]="!changeCreative ?'envio_criativo' : 'substituicao_criativos'"
            [attr.data-section]="!changeCreative ? 'finalizacao' : 'finalizacao_substituicao'"
            [attr.data-label]="!changeCreative ? 'concluir' : 'substituir_criativo'"
            id="button_send-criative"
            glbButton
            theme="purple-gradient"
            [disabled]="disabled"
            (click)="handleSend()"
            data-element="button"
            data-state="activated"
            data-area="associacao_criativos"
            data-section="finalizacao_associacao"
            [attr.data-label]="!changeCreative ? 'Concluir' : 'Substituir Criativo' "
          >
            {{ !changeCreative ? 'Concluir' : 'Substituir Criativo' }}
          </button>
        </div>
      </footer>
    </div>

    <app-gerador-criativos></app-gerador-criativos>
  </div>
</main>
