import { Injectable } from '@angular/core'
import { DiaDoMes } from '../models/dia-mes'
import { DiaSemanaEnum, DiaSemana } from '../models/dia-semana'

@Injectable()
export class Calendario {
  diasSemana: DiaSemana[] = [
    {
      label: 'DOMINGO',
      firstLetter: 'D',
      value: 0,
      diaSemana: DiaSemanaEnum.dom,
    },
    {
      label: 'SEGUNDA',
      firstLetter: 'S',
      value: 1,
      diaSemana: DiaSemanaEnum.seg,
    },
    {
      label: 'TERÇA',
      firstLetter: 'T',
      value: 2,
      diaSemana: DiaSemanaEnum.ter,
    },
    {
      label: 'QUARTA',
      firstLetter: 'Q',
      value: 3,
      diaSemana: DiaSemanaEnum.qua,
    },
    {
      label: 'QUINTA',
      firstLetter: 'Q',
      value: 4,
      diaSemana: DiaSemanaEnum.qui,
    },
    {
      label: 'SEXTA',
      firstLetter: 'S',
      value: 5,
      diaSemana: DiaSemanaEnum.sex,
    },
    {
      label: 'SÁBADO',
      firstLetter: 'S',
      value: 6,
      diaSemana: DiaSemanaEnum.sab,
    },
  ]

  gerarDiasDoMes(data: Date): DiaDoMes[] {
    if (data !== undefined) {
      const diasDoMes: DiaDoMes[] = []
      const qtdDias = new Date(
        data.getFullYear(),
        data.getMonth() + 1,
        0,
      ).getDate()
      const dias = Array.from(Array(qtdDias).keys())
      dias.forEach(dia => {
        const diaCerto = dia + 1
        const d = new Date(data.getFullYear(), data.getMonth(), diaCerto)
        const diaSemana = this.diasSemana.find(
          diaSemana => diaSemana.value === d.getDay(),
        )
        diasDoMes.push({
          numero: diaCerto,
          diaSemana,
          id: d.toString(),
          date: d,
        })
      })
      while (diasDoMes[0].diaSemana.value !== 0) {
        const diaSemanaValue = diasDoMes[0].diaSemana.value
        const diaSemana = this.diasSemana.find(
          diaSemana => diaSemana.value === diaSemanaValue - 1,
        )
        diasDoMes.unshift({
          numero: null,
          diaSemana,
        })
      }
      while (diasDoMes[diasDoMes.length - 1].diaSemana.value !== 6) {
        const diaSemanaValue = diasDoMes[diasDoMes.length - 1].diaSemana.value
        const diaSemana = this.diasSemana.find(
          diaSemana => diaSemana.value === diaSemanaValue + 1,
        )
        diasDoMes.push({
          numero: null,
          diaSemana,
        })
      }
      return diasDoMes
    }
    return null
  }

  gerarNamedVeiculacoes(diaDoMesInsercao: any) {
    const diaDoMesInsercaoKeys = Object.keys(diaDoMesInsercao)
    const veiculacoes = []
    diaDoMesInsercaoKeys.forEach(dia => {
      const date = new Date(dia).toISOString().split('T')[0]
      const finalDate = this.getFormattedDate(date)
      veiculacoes.push({
        dia: finalDate,
        exibicoes: diaDoMesInsercao[dia],
        date,
      })
    })
    const namedVeiculacoes = veiculacoes.sort((a, b) =>
      a.date < b.date ? -1 : 1,
    )
    return namedVeiculacoes
  }

  getFormattedDate(date) {
    const splittedDate = date.split('-')
    const day = splittedDate[2]
    const monthName = this.getMonthName(parseInt(splittedDate[1]))
    return `${day} de ${monthName}`
  }

  getMonthName(month) {
    switch (month) {
      case 1:
        return 'Janeiro'
      case 2:
        return 'Fevereiro'
      case 3:
        return 'Março'
      case 4:
        return 'Abril'
      case 5:
        return 'Maio'
      case 6:
        return 'Junho'
      case 7:
        return 'Julho'
      case 8:
        return 'Agosto'
      case 9:
        return 'Setembro'
      case 10:
        return 'Outubro'
      case 11:
        return 'Novembro'
      case 12:
        return 'Dezembro'
      default:
        return 'Eh o dj gbr'
    }
  }

  getSplittedDate(date: string) {
    if (!date) return null
    const splittedDate = date.split('-')
    const day = splittedDate[2]
    const month = splittedDate[1]
    const year = splittedDate[0]
    return {
      day,
      month,
      year,
    }
  }

  datetimeToDateMonth(date: Date, minusOne?: boolean) {
    if (!date) return null
    const fullYear = date.getFullYear().toString()
    let month: any = date.getMonth() + 1
    if (minusOne) {
      month -= 1
    }
    month = month.toString()
    if (month.length == 1) {
      month = `0${month}`
    }
    return `${fullYear}-${month}`
  }

  brDateToDate(brDate: string) {
    if (!brDate) return null
    const dateSplit = brDate.split('/')
    const year = dateSplit[2]
    const month = dateSplit[1]
    const day = dateSplit[0]
    return `${year}-${month}-${day}`
  }

  dateToBrDate(date: string) {
    if (!date) return null
    const splittedDate = date.split('-')
    const day = splittedDate[2]
    const month = splittedDate[1]
    const year = splittedDate[0]
    return `${day}/${month}/${year}`
  }

  validateBrDate(date: string): boolean {
    if (!date || date.trim().length < 10) return false

    const splittedDate = date.split('/')
    const day = splittedDate[0]
    const month = splittedDate[1]
    const year = splittedDate[2]

    const finalDate = Date.parse(`${year}-${month}-${day}T00:00`)
    if (!finalDate || finalDate == undefined || finalDate == null) {
      return false
    } else {
      return true
    }

    /*if (finalDate instanceof Date) {
            return finalDate.getDate() === parseInt(day);
        } else {
            return false;
        }*/
  }
}
