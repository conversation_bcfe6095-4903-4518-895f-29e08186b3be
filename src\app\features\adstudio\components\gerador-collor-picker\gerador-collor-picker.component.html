<div
  class="collor-picker-container d-flex justify-content-between align-items-center"
  data-element="input"
  data-state="activated"
  data-area="gerador_criativos"
  [attr.data-section]="'3_text_customizacao_cores_layout'"
  [attr.data-label]="'color_name'"
  [attr.data-adicionalinfo]="color.label"
  [attr.data-keyword]="color.label"
>
  <p
    *ngIf="color.deletable; else defaultText"
    data-element="input"
    data-state="activated"
    data-area="gerador_criativos"
    [attr.data-section]="'3_text_customizacao_cores_layout'"
    [attr.data-label]="'color_name'"
    [attr.data-adicionalinfo]="color.label"
    [attr.data-keyword]="color.label"
  >
    {{ color.label }}
  </p>

  <ng-template #defaultText>
    <p
      data-element="input"
      data-state="activated"
      data-area="gerador_criativos"
      [attr.data-section]="'3_text_customizacao_cores_layout'"
      [attr.data-label]="'color_name'"
      [attr.data-adicionalinfo]="color.label"
      [attr.data-keyword]="color.label"
    >
      {{ color.id }}. {{ color.label }}
    </p>
  </ng-template>

  <div
    class="picker-container d-flex justify-content-center align-items-center"
  >
    <ngx-colors
      ngx-colors-trigger
      [hideTextInput]="true"
      [hideColorPicker]="false"
      [(ngModel)]="color.value"
      (input)="colorChanging()"
      data-element="input"
      data-state="activated"
      data-area="gerador_criativos"
      [attr.data-section]="'3_text_customizacao_cores_layout'"
      [attr.data-label]="'Adicionar cor'"
      [attr.data-adicionalinfo]="'button_adicionar_cor'"
      [attr.data-keyword]="'adicionar_cor'"
    >
    </ngx-colors>

    <div
      class="icon-delete"
      data-element="button"
      data-state="activated"
      data-area="gerador_criativos"
      [attr.data-section]="'3_text_customizacao_cores_layout'"
      [attr.data-label]="'Deletar cor'"
      [attr.data-adicionalinfo]="'button_deletar_cor'"
      [attr.data-keyword]="'deletar_cor'"
      *ngIf="color.deletable"
      (click)="removeItem()"
    ></div>
  </div>
</div>
