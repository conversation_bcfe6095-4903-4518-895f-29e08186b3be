/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Component,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core'
import { ImgCardSettings } from '../img-card/model/img-card-settings'
import { SlideCardStateService } from '../slide-card/slide-card-state/slide-card-state.service'
import { VideoBanner } from '../video-banner/model/video-banner'
import { NotificationService } from '@app/core/services/notification/notification.service'
import { EducationState } from './education-state/education-state.service'
import { Subscription } from 'rxjs'
import {
  Education,
  EducationCards,
  EducationTypes,
} from './model/education-types'
import {
  educationAutoTopics,
  educationCustomTopics,
  educationMidiaTopics,
  educationProducerTopics,
  educationUploadTopics,
} from './constants/constants-tv'
import {
  educationCreatorTopics,
  educationPlanDigital,
  educationUploadDigital,
} from './constants/constants-digital'
import {
  DIALOG_CONDECINE_DEFAULT,
  TITLE_CONDECINE,
} from '@app/shared/utils/swal-templates'
import { PartnerTypes } from '@app/features/payment/model/checkout-payload'
import { VideoSettingsForCheckout } from '@app/features/fluxo-video/material-flow/models/video-settings-for-checkout'
import { Router } from '@angular/router'
import { GtmHandlerService } from '@app/core/services/gtm/gtm-handler.service'
import { UserInfoResponse } from '@app/shared/models/invites'
import { educationBoleto15Fm } from './constants/constants-pagamento'
import { ProducerTypes } from '@app/core/services/producer/producer'
import { Topic } from '../topics-steps/model/Itopics'
import { uploadVideoTV } from './constants/constant-video-upload'
import { DetailGTMSlide } from './model/education-gtm'
import { SlideCardComponent } from '../slide-card/slide-card.component'

interface EducationType {
  id: EducationCards
  value: Education
  steps: Array<Array<Topic>>
}

@Component({
  selector: 'app-education-side-menu',
  templateUrl: './education-side-menu.component.html',
  styleUrls: ['./education-side-menu.component.scss'],
})
export class EducationSideMenuComponent implements OnInit, OnDestroy {
  @ViewChild(SlideCardComponent) slideCardComponent!: SlideCardComponent

  @HostListener('click', ['$event.target'])
  onClick(element: HTMLElement) {
    if (element.className.includes('condecin')) {
      this.openCondecineModal()
    }
  }
  @Input() userInfo: UserInfoResponse

  currentPosition = 0

  isOpen = false

  labelsButton = {
    open: 'exibir',
    closed: 'ocultar',
  }

  subs = new Subscription()

  educationType: EducationTypes

  educationInfoGtm: DetailGTMSlide = {
    dataArea: 'home_education',
    dataSection: 'aprender',
    dataLabel: 'aprenda_anunciar',
  }

  educationPath = 'Jornada de TV'
  educationPathDigital = 'Jornada digital'

  headerList = {
    VIDEO_CREATE: {
      title: 'Crie você mesmo',
      subTitle: this.educationPath,
      actionLabel: 'voltar',
      handleAction: () => this.returnToFirstPosition(),
      backgroundColor: ' ',
      backgroundIconPath: './assets/imgs/education/education-crie-gradient.svg',
    },
    VIDEO_PRODUCER: {
      title: 'Produtora indicada',
      subTitle: this.educationPath,
      actionLabel: 'voltar',
      handleAction: () => this.returnToFirstPosition(),
      backgroundColor: ' ',
      backgroundIconPath:
        './assets/imgs/education/produtora-indica-gradient.svg',
    },
    VIDEO_UPLOAD: {
      title: 'Envie seu vídeo',
      subTitle: this.educationPath,
      actionLabel: 'voltar',
      handleAction: () => this.returnToFirstPosition(),
      backgroundColor: ' ',
      backgroundIconPath:
        './assets/imgs/education/envie-seu-video-gradiente.svg',
    },
    TV_CUSTOM: {
      title: 'Plano personalizado',
      subTitle: this.educationPath,
      actionLabel: 'voltar',
      handleAction: () => this.returnToFirstPosition(),
      backgroundColor: ' ',
      backgroundIconPath:
        './assets/imgs/education/plano-personalizado-gradient.svg',
    },
    TV_AUTO: {
      title: 'Plano automático',
      subTitle: this.educationPath,
      actionLabel: 'voltar',
      handleAction: () => this.returnToFirstPosition(),
      backgroundColor: ' ',
      backgroundIconPath:
        './assets/imgs/education/plano-automatico-gradient.svg',
    },
    DIGITAL_PLAN: {
      title: 'Planejar seu anuncio',
      subTitle: this.educationPathDigital,
      actionLabel: 'voltar',
      handleAction: () => this.returnToFirstPosition(),
      backgroundColor: ' ',
      backgroundIconPath:
        './assets/imgs/education/planejar-seu-anuncio-gradient.svg',
    },
    DIGITAL_UPLOAD: {
      title: 'Envio de criativos',
      subTitle: this.educationPathDigital,
      actionLabel: 'voltar',
      handleAction: () => this.returnToFirstPosition(),
      backgroundColor: ' ',
      backgroundIconPath: './assets/imgs/education/envio-criativo-gradient.svg',
    },
    BOLETO_15FM: {
      title: 'Limite pós-pago',
      subTitle: 'pagamento',
      handleAction: () => this.closeCard(),
      backgroundColor: '#FAFAFA',
      backgroundIconPath: './assets/imgs/education/thumb-ed-boleto.svg',
    },
    UPLOAD_VIDEO_TV: {
      title: 'Envie seu vídeo',
      subTitle: 'Jornada de tv',
      handleAction: () => this.closeCard(),
      backgroundColor: ' ',
      icon: 'back-btn-black',
      backgroundIconPath: './assets/imgs/education/thumb-ed-upload-video.svg',
    },
    CREATOR_ADSTUDIO: {
      title: 'Gere o seu criativo',
      subTitle: 'Digital',
      handleAction: () => this.closeCard(),
      backgroundColor: ' ',
      icon: 'back-btn-black',
      backgroundIconPath: './assets/imgs/education/item-thumb-gerador.svg',
    },
  }

  createVideo: ImgCardSettings = {
    handleClick: () => (this.currentPosition = this.cardTypes.CREATE_VIDEO),
    imagePath: './assets/imgs/education/crie-voce-mesmo-education.svg',
    description: 'Crie o seu vídeo utilizando templates prontos',
    imgAlt: 'Crie você mesmo',
    sectionGtm: 'video',
    labelGtm: 'crie_voce_mesmo',
    areaGtm: 'home_education',
  }

  videoFromProducer: ImgCardSettings = {
    handleClick: () => (this.currentPosition = this.cardTypes.PRODUCER),
    imagePath: './assets/imgs/education/produtora-indicada-education.svg',
    description: 'Crie o seu vídeo através da nossa produtora indicada',
    imgAlt: 'Produtora Indicada',
    sectionGtm: 'video',
    labelGtm: 'produtora_indicada',
    areaGtm: 'home_education',
  }

  upload: ImgCardSettings = {
    handleClick: () => (this.currentPosition = this.cardTypes.UPLOAD),
    imagePath: './assets/imgs/education/envie-seu-video-education.svg',
    description: 'Já tem um vídeo pronto? Faça o upload para começar',
    imgAlt: 'Envie seu vídeo',
    sectionGtm: 'video',
    labelGtm: 'envie_seu_video',
    areaGtm: 'home_education',
  }

  autoMidia: ImgCardSettings = {
    handleClick: () => (this.currentPosition = this.cardTypes.AUTO_MIDIA),
    imagePath: './assets/imgs/education/plano-automatico-education.svg',
    description: 'Sugestões de programas a partir do seu objetivo',
    imgAlt: 'PLANO DE MÍDIA AUTO',
    sectionGtm: 'plano_de_midia',
    labelGtm: 'plano_automatico',
    areaGtm: 'home_education',
  }

  customMidia: ImgCardSettings = {
    handleClick: () => (this.currentPosition = this.cardTypes.CUSTOM_MIDIA),
    imagePath: './assets/imgs/education/plano-personalizado-education.svg',
    description: 'Escolha programas específicos para anunciar ',
    imgAlt: 'PLANO DE MÍDIA',
    sectionGtm: 'plano_de_midia',
    labelGtm: 'plano_personalizado',
    areaGtm: 'home_education',
  }

  digitalPlan: ImgCardSettings = {
    handleClick: () => (this.currentPosition = this.cardTypes.DIGITAL_PLAN),
    imagePath: './assets/imgs/education/planejando-seu-anuncio-education.svg',
    description: 'Defina o objetivo, segmentação e as datas do seu anúncio',
    imgAlt: 'PLANEJAR SEU ANUNCIO DIGITAL',
    sectionGtm: 'digital',
    labelGtm: 'planejar_seu_anuncio',
    areaGtm: 'home_education',
  }

  digitalUpload: ImgCardSettings = {
    handleClick: () => (this.currentPosition = this.cardTypes.DIGITAL_UPLOAD),
    imagePath: './assets/imgs/education/envio-de-criativos.svg',
    description: 'Selecione e vincule os criativos ao seu anúncio',
    imgAlt: 'ENVIO DE CRIATIVOS DIGITAL',
    sectionGtm: 'digital',
    labelGtm: 'envio_criativos',
    areaGtm: 'home_education',
  }

  digitalCreator: ImgCardSettings = {
    handleClick: () => (this.currentPosition = this.cardTypes.CREATOR_ADSTUDIO),
    imagePath: './assets/imgs/education/thumb-ed-gerador.svg',
    description: 'Não tem um criativo? Crie o seu com a IA da Globo.',
    imgAlt: 'GERADOR DE CRIATIVOS DIGITAL',
    sectionGtm: 'digital',
    labelGtm: 'gerador_criativos',
    areaGtm: 'home_education',
  }
  videoBannerSettings: VideoBanner = {
    title: 'Vamos começar?',
    desc: 'A gente vai te mostrar como anunciar na Globo.',
    imgPath: './assets/imgs/actor-background.svg',
    imgAlt: 'Vamos Começar',
    handleAction: () =>
      this.notificationService.globoSimGloboiVideoLib(1608952),
  }

  educationTypesList: Array<EducationType> = [
    {
      id: this.cardTypes.CREATE_VIDEO,
      value: this.educationTypes.VIDEO_CREATE,
      steps: educationMidiaTopics,
    },
    {
      id: this.cardTypes.PRODUCER,
      value: this.educationTypes.VIDEO_PRODUCER,
      steps: educationProducerTopics,
    },
    {
      id: this.cardTypes.UPLOAD,
      value: this.educationTypes.VIDEO_UPLOAD,
      steps: educationUploadTopics,
    },
    {
      id: this.cardTypes.AUTO_MIDIA,
      value: this.educationTypes.TV_AUTO,
      steps: educationAutoTopics,
    },
    {
      id: this.cardTypes.CUSTOM_MIDIA,
      value: this.educationTypes.TV_CUSTOM,
      steps: educationCustomTopics,
    },
    {
      id: this.cardTypes.DIGITAL_PLAN,
      value: this.educationTypes.DIGITAL_PLAN,
      steps: educationPlanDigital,
    },
    {
      id: this.cardTypes.DIGITAL_UPLOAD,
      value: this.educationTypes.DIGITAL_UPLOAD,
      steps: educationUploadDigital,
    },
    {
      id: this.cardTypes.BOLETO_15FM,
      value: this.educationTypes.BOLETO_15FM,
      steps: educationBoleto15Fm,
    },
    {
      id: this.cardTypes.UPLOAD_VIDEO_TV,
      value: this.educationTypes.UPLOAD_VIDEO_TV,
      steps: uploadVideoTV,
    },
    {
      id: this.cardTypes.CREATOR_ADSTUDIO,
      value: this.educationTypes.CREATOR_ADSTUDIO,
      steps: educationCreatorTopics,
    },
  ]
  receivedDataGtm: { dataArea: string; dataSection: string; dataLabel: string }

  constructor(
    private slideCardStateService: SlideCardStateService,
    private notificationService: NotificationService,
    private educationState: EducationState,
    private router: Router,
    private gtmHandlerService: GtmHandlerService,
    private renderer: Renderer2,
  ) {}

  get educationTypes() {
    return Education
  }

  get cardTypes() {
    return EducationCards
  }

  ngOnInit() {
    this.watchEducationState()
    this.watchSlideCard()
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe()
  }

  returnToFirstPosition() {
    this.currentPosition = 0
  }

  closeCard() {
    this.slideCardStateService.setCardToggleSubject(false)
  }

  watchSlideCard() {
    this.subs.add(
      this.slideCardStateService.cardToggleSubject$.subscribe(() => {
        this.educationState.setEducationPosition(0)
      }),
    )
  }

  private watchEducationState() {
    this.subs.add(
      this.educationState.educationSubject$.subscribe(
        response => (this.educationType = response),
      ),
    )
    this.subs.add(
      this.educationState.educationPositionSubject$.subscribe(
        response => (this.currentPosition = response),
      ),
    )
  }

  openCondecineModal() {
    this.notificationService.globoSimCondecine(
      TITLE_CONDECINE,
      DIALOG_CONDECINE_DEFAULT,
      'Entendi',
    )
  }

  redirectTo(type: Education) {
    this.closeCard()
    setTimeout(() => {
      switch (type) {
        case this.educationTypes.VIDEO_CREATE:
          this.moveToNextStep(ProducerTypes.AC)
          break
        case this.educationTypes.VIDEO_PRODUCER:
          this.moveToNextStep(ProducerTypes.PB)
          break
        case this.educationTypes.VIDEO_UPLOAD:
          this.moveToNextStep(ProducerTypes.PV)
          break
        case this.educationTypes.TV_AUTO:
          this.router.navigate(['configuracao-campanha/plano/automatico'])
          break
        case this.educationTypes.TV_CUSTOM:
          this.router.navigate(['configuracao-campanha'])
          break
        case this.educationTypes.DIGITAL_PLAN:
        case this.educationTypes.DIGITAL_UPLOAD:
          break
        case this.educationTypes.CREATOR_ADSTUDIO:
          this.router.navigate(['/digital/anuncio-digital/objetivo'])
          break
      }
    }, 600)
  }

  private moveToNextStep(type: PartnerTypes) {
    const videoSettings: VideoSettingsForCheckout = {
      partner: type,
    }
    this.router.navigate(['/material/video-info'], {
      queryParams: { settings: btoa(JSON.stringify(videoSettings)) },
    })
  }

  gtmEvents(idButton: string, card: ImgCardSettings) {
    this.gtmHandlerService.buttonClickEvent(
      this.userInfo,
      idButton,
      true,
      'education_modal',
    )
    this.gtmHandlerService.viewInformationEvent(
      this.userInfo,
      card.description,
      'education_modal',
    )
  }
  setTextConfirmButton(type: Education) {
    switch (type) {
      case this.educationTypes.UPLOAD_VIDEO_TV:
        return 'FECHAR'
      default:
        return 'COMEÇAR'
    }
  }
  handleGtmData(event: {
    dataArea: string
    dataSection: string
    dataLabel: string
  }) {
    this.receivedDataGtm = event
  }
  nextAndPreviousHasClicked(event) {
    this.slideCardComponent.changeClass()
  }
}
