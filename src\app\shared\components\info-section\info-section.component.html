<div
  class="flex p-6 rounded-2xl border border-neutral-200 w-full justify-between"
>
  <div class="flex gap-8 justify-between">
    <div class="flex justify-center items-center">
      <mat-icon
        *ngIf="iconName"
        [svgIcon]="iconName"
        alt="Icone"
        class="h-12 w-12"
      ></mat-icon>
      <mat-icon *ngIf="!iconName" svgIcon="info-section"></mat-icon>
    </div>
    <div class="flex flex-col gap-1">
      <h1 class="font-book font-semibold text-60 leading-6 text-neutral-900">
        {{ title }}
      </h1>
      <p class="font-book font-normal text-40 leading-6 text-neutral-700">
        {{ description }}
      </p>
    </div>
  </div>
  <div>
    <button
      (click)="handleButtonClick()"
      class="px-6 py-3 border border-fill-accent-gradient-p33 rounded-lg flex gap-2 font-display font-semibold text-sm leading-6 items-center text-fill-accent-gradient-p33 uppercase"
      data-element="button"
      data-state="activated"
      data-area="associacao_criativos"
      data-section="selecao_criativos"
      [attr.data-label]="buttonText"
    >
      {{ buttonText }}
    </button>
  </div>
</div>
