export const capitalizeFirstLetters = (value: string) => {
  const result = value
    .toLowerCase()
    .replace(/(?:^|\s)(?!da|de|do)\S/g, l => l.toUpperCase())
  return result
}

export const replaceAllMatches = (
  text: string,
  target: string,
  replacement: string,
) => {
  const regexAll = new RegExp(target, 'g')

  return text.replace(regexAll, replacement)
}

export const snakeCase = text => {
  if (!text) return text
  return text
    .normalize('NFD')
    .replace(/[^\w\s.]/g, '')
    .replace(/[\s.]+/g, '_')
    .toLowerCase()
}
export const moedaBrasileira = (value: number) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value)
}

export const capitalizeFirstLetter = (value: string) => {
  if (!value) return value
  return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase()
}
export const dateTransform = isoString => {
  const date = new Date(isoString)
  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = date.getFullYear()
  return `${day}/${month}/${year}`
}
